-- Test script for new range queries and composite indexes
print("🧪 Testing New Features: Range Queries and Composite Indexes")
print("=" .. string.rep("=", 60))

-- Test 1: Range Queries
print("\n1️⃣  Testing Range Queries...")
db:create_table("employees")
db:create_index("employees", "age")
db:create_index("employees", "salary")

-- Insert test data
local employees = {
    {name = "<PERSON>", age = 25, salary = 50000, department = "Engineering"},
    {name = "<PERSON>", age = 30, salary = 60000, department = "Engineering"},
    {name = "<PERSON>", age = 35, salary = 70000, department = "Marketing"},
    {name = "<PERSON>", age = 40, salary = 80000, department = "Sales"},
    {name = "Eve", age = 28, salary = 55000, department = "Engineering"}
}

local ids = db:insert_rows("employees", employees)
print("✅ Inserted", #ids, "employees")

-- Test age range queries
print("\n📊 Age Range Queries:")
local young_adults = db:select_by_index_range("employees", "age", 25, 30)
print("✅ Employees aged 25-30:", #young_adults, "found")
for _, emp in ipairs(young_adults) do
    print("   -", emp.name, "(age " .. emp.age .. ")")
end

local seniors = db:select_by_index_greater_than("employees", "age", 30)
print("✅ Employees older than 30:", #seniors, "found")
for _, emp in ipairs(seniors) do
    print("   -", emp.name, "(age " .. emp.age .. ")")
end

local juniors = db:select_by_index_less_than("employees", "age", 30)
print("✅ Employees younger than 30:", #juniors, "found")
for _, emp in ipairs(juniors) do
    print("   -", emp.name, "(age " .. emp.age .. ")")
end

-- Test salary range queries
print("\n💰 Salary Range Queries:")
local mid_range_salary = db:select_by_index_range("employees", "salary", 55000, 70000)
print("✅ Employees earning $55k-$70k:", #mid_range_salary, "found")
for _, emp in ipairs(mid_range_salary) do
    print("   -", emp.name, "($" .. emp.salary .. ")")
end

-- Test 2: Composite Indexes
print("\n2️⃣  Testing Composite Indexes...")
db:create_table("products")
db:create_composite_index("products", "category_price", {"category", "price_range"})
db:create_composite_index("products", "brand_category", {"brand", "category"})

-- Insert product data
local products = {
    {name = "Laptop Pro", category = "Electronics", brand = "TechCorp", price = 1200, price_range = "High"},
    {name = "Budget Laptop", category = "Electronics", brand = "ValueTech", price = 400, price_range = "Low"},
    {name = "Gaming Mouse", category = "Electronics", brand = "TechCorp", price = 80, price_range = "Medium"},
    {name = "Office Chair", category = "Furniture", brand = "ComfortCorp", price = 300, price_range = "Medium"},
    {name = "Standing Desk", category = "Furniture", brand = "ComfortCorp", price = 600, price_range = "High"}
}

local product_ids = db:insert_rows("products", products)
print("✅ Inserted", #product_ids, "products")

-- Test composite index queries
print("\n🔍 Composite Index Queries:")

-- Query by category and price range
local high_electronics = db:select_by_composite_index("products", "category_price", {
    category = "Electronics",
    price_range = "High"
})
print("✅ High-priced Electronics:", #high_electronics, "found")
for _, product in ipairs(high_electronics) do
    print("   -", product.name, "($" .. product.price .. ")")
end

-- Query by brand and category
local techcorp_electronics = db:select_by_composite_index("products", "brand_category", {
    brand = "TechCorp",
    category = "Electronics"
})
print("✅ TechCorp Electronics:", #techcorp_electronics, "found")
for _, product in ipairs(techcorp_electronics) do
    print("   -", product.name, "($" .. product.price .. ")")
end

-- Partial composite query (only category)
local all_electronics = db:select_by_composite_index("products", "category_price", {
    category = "Electronics"
})
print("✅ All Electronics (partial query):", #all_electronics, "found")
for _, product in ipairs(all_electronics) do
    print("   -", product.name, "(" .. product.price_range .. " price)")
end

print("\n🎉 All new features tested successfully!")
print("✅ Range Queries: Working")
print("✅ Composite Indexes: Working")
