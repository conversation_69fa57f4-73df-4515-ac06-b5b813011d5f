-- init.lua - Auto-loaded when ExtensibleDB starts
-- This file runs automatically every time you start the program

print("🚀 ExtensibleDB initialized!")
print("📁 Database location: ./data.db/")

-- Create default tables if they don't exist
db:create_table("users")
db:create_table("logs")

-- Define utility functions available in the console
function add_user(name, email, age)
    local user = {
        name = name,
        email = email,
        age = age,
        created_at = os.time(),
        id = math.random(1000000, 9999999)
    }
    db:insert_row("users", user)
    print("✅ Added user: " .. name .. " (ID: " .. user.id .. ")")
    return user.id
end

function list_users()
    local users = db:select_all("users")
    print("\n👥 Users in database:")
    print("ID       | Name           | Email                | Age")
    print("---------|----------------|----------------------|----")
    
    for _, user in ipairs(users) do
        local id = user.id or "N/A"
        local name = user.name or "Unknown"
        local email = user.email or "No email"
        local age = user.age or "N/A"
        
        print(string.format("%-8s | %-14s | %-20s | %s", 
              id, name, email, age))
    end
    print("")
end

function log_event(event, details)
    local log_entry = {
        event = event,
        details = details or "",
        timestamp = os.time(),
        date = os.date("%Y-%m-%d %H:%M:%S")
    }
    db:insert_row("logs", log_entry)
    print("📝 Logged: " .. event)
end

function show_stats()
    local users = db:select_all("users")
    local logs = db:select_all("logs")
    
    print("\n📊 Database Statistics:")
    print("Users: " .. #users)
    print("Log entries: " .. #logs)
    
    if #users > 0 then
        local total_age = 0
        local age_count = 0
        
        for _, user in ipairs(users) do
            if user.age then
                total_age = total_age + user.age
                age_count = age_count + 1
            end
        end
        
        if age_count > 0 then
            local avg_age = total_age / age_count
            print("Average user age: " .. string.format("%.1f", avg_age))
        end
    end
    print("")
end

-- Log that initialization completed
log_event("system_init", "ExtensibleDB started and initialized")

print("🔧 Available functions: add_user(), list_users(), log_event(), show_stats()")
print("💡 Type 'show_stats()' to see database info")
print("")
