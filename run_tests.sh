#!/bin/bash

# ExtensibleDB Test Runner
# Runs all tests and examples to verify functionality

set -e  # Exit on any error

echo "🧪 ExtensibleDB Test Suite"
echo "=========================="

# Clean up any existing database
echo "🧹 Cleaning up previous test data..."
rm -rf data.db/

# Build the project
echo "🔨 Building ExtensibleDB..."
cargo build --release

# Run unit tests
echo "🦀 Running Rust unit tests..."
cargo test

# Run Lua integration tests
echo "🌙 Running Lua integration tests..."

echo "  📝 Basic functionality test..."
cargo run --release tests/test_basic.lua

echo "  ⚡ Enhanced features test..."
cargo run --release tests/test_enhanced.lua

echo "  🔍 Secondary indexes test..."
cargo run --release tests/test_indexes.lua

echo "  🎯 Comprehensive feature test..."
cargo run --release tests/test_comprehensive.lua

# Run examples
echo "🚀 Running examples..."

echo "  📊 Performance benchmarks..."
cargo run --release examples/benchmark.lua

echo "  🎪 Feature demonstration..."
cargo run --release examples/demo.lua

echo ""
echo "✅ All tests passed successfully!"
echo "🎉 ExtensibleDB is working correctly!"
echo ""
echo "💡 Try the interactive console: cargo run"
echo "📚 Read the README.md for full documentation"