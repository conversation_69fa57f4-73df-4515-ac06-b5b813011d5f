-- Debug composite indexes - test order of operations
print("🔍 Debugging Composite Indexes - Order Test")

-- Clean start
db:create_table("order_test")

-- Test 1: Create index BEFORE inserting data
print("\n1️⃣ Test: Create index BEFORE inserting data")
db:create_composite_index("order_test", "before_index", {"category", "price_range"})
print("✅ Created composite index")

local id1 = db:insert_row("order_test", {
    name = "Item1",
    category = "Electronics", 
    price_range = "High"
})
print("✅ Inserted item1 with ID:", id1)

local results1 = db:select_by_composite_index("order_test", "before_index", {
    category = "Electronics",
    price_range = "High"
})
print("📊 Query results (index before insert):", #results1)

-- Test 2: Create index AFTER inserting data  
print("\n2️⃣ Test: Create index AFTER inserting data")
db:create_table("order_test2")

local id2 = db:insert_row("order_test2", {
    name = "Item2",
    category = "Electronics",
    price_range = "High"
})
print("✅ Inserted item2 with ID:", id2)

db:create_composite_index("order_test2", "after_index", {"category", "price_range"})
print("✅ Created composite index")

local results2 = db:select_by_composite_index("order_test2", "after_index", {
    category = "Electronics", 
    price_range = "High"
})
print("📊 Query results (index after insert):", #results2)

-- Check what's in the tables
print("\n📊 Table contents:")
local all1 = db:select_all("order_test")
print("order_test rows:", #all1)
for _, row in ipairs(all1) do
    print("  ", row.name, row.category, row.price_range)
end

local all2 = db:select_all("order_test2") 
print("order_test2 rows:", #all2)
for _, row in ipairs(all2) do
    print("  ", row.name, row.category, row.price_range)
end
