//! ExtensibleDB – An extensible, embedded database engine written in Rust.

use anyhow::{Context, Result};
use mlua::{Error as <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>aSerdeExt, <PERSON>rData, UserDataMethods, Value as LuaValue};
use serde_json::Value;
use sled::Db;
use std::collections::HashMap;
use std::env;
use std::fs;
use std::sync::atomic::{AtomicBool, Ordering};

// Dedicated tree name that stores index catalog information so that indexes
// survive database restarts.  A key will look like "<table>:<field>" and the
// value is an empty byte slice (we only care about key presence).
const META_INDEXES_TREE: &str = "__meta_indexes";

/// Operation captured while a client-side transaction is open.  When
/// `commit_transaction` is invoked the `Database` will replay the operations
/// against `sled`.  This is **not** as strong as real MVCC but provides a
/// familiar begin/commit/rollback API that is good enough for single-threaded
/// scripting from Lua.
enum PendingOp {
    InsertRow {
        table: String,
        key: Vec<u8>,
        data: Vec<u8>,
    },
    IndexInsert {
        table: String,
        field: String,
        idx_key: Vec<u8>,
    },
}
use parking_lot::RwLock;
use std::io::{self, Write};
use std::path::Path;
use std::sync::Arc;

pub struct Database {
    storage: Db,
    // Track which indexes exist for each table
    indexes: Arc<RwLock<HashMap<String, Vec<String>>>>,
    // --- Transaction state ---
    transaction_active: Arc<AtomicBool>,
    pending_ops: Arc<Mutex<Vec<PendingOp>>>, // guarded list of buffered operations
}

impl Clone for Database {
    fn clone(&self) -> Self {
        Self {
            storage: self.storage.clone(),
            indexes: self.indexes.clone(),
            transaction_active: self.transaction_active.clone(),
            pending_ops: self.pending_ops.clone(),
        }
    }
}

fn idx_tree_name(table: &str, field: &str) -> String {
    format!("idx:{}:{}", table, field)
}

impl UserData for Database {
    fn add_methods<M: UserDataMethods<Self>>(methods: &mut M) {
        methods.add_method("create_table", |_, this, name: String| {
            this.create_table(&name).map_err(LuaError::external)?;
            Ok(())
        });

        methods.add_method(
            "insert_row",
            |lua, this, (table, row): (String, LuaValue)| {
                let json_row: Value = lua.from_value(row)?;
                let id = this
                    .insert_row(&table, json_row)
                    .map_err(LuaError::external)?;
                lua.to_value(&id)
            },
        );

        methods.add_method(
            "insert_rows",
            |lua, this, (table, rows): (String, Vec<LuaValue>)| {
                let json_rows: Result<Vec<Value>, _> =
                    rows.into_iter().map(|row| lua.from_value(row)).collect();
                let json_rows = json_rows?;
                let ids = this
                    .insert_rows(&table, json_rows)
                    .map_err(LuaError::external)?;
                lua.to_value(&ids)
            },
        );

        methods.add_method("select_all", |lua, this, table: String| {
            let rows = this.select_all(&table).map_err(LuaError::external)?;
            lua.to_value(&rows)
        });

        methods.add_method(
            "create_index",
            |_, this, (table, field): (String, String)| {
                this.create_index(&table, &field)
                    .map_err(LuaError::external)?;
                Ok(())
            },
        );

        methods.add_method(
            "select_by_index",
            |lua, this, (table, field, value): (String, String, LuaValue)| {
                let json_val: Value = lua.from_value(value)?;
                let rows = this
                    .select_by_index(&table, &field, &json_val)
                    .map_err(LuaError::external)?;
                lua.to_value(&rows)
            },
        );

        methods.add_method("begin_transaction", |_, this, ()| {
            this.begin_transaction().map_err(LuaError::external)?;
            Ok(())
        });

        methods.add_method("commit_transaction", |_, this, ()| {
            this.commit_transaction().map_err(LuaError::external)?;
            Ok(())
        });

        methods.add_method("rollback_transaction", |_, this, ()| {
            this.rollback_transaction().map_err(LuaError::external)?;
            Ok(())
        });
    }
}

fn main() -> Result<()> {
    let db = Database::open("data.db")?;
    let lua = Lua::new();
    lua.globals().set("db", db)?;

    if Path::new("init.lua").exists() {
        println!("Loading init.lua...");
        let init_script = fs::read_to_string("init.lua")?;
        lua.load(&init_script).exec()?;
    }

    let args: Vec<String> = env::args().collect();
    if args.len() > 1 {
        let lua_file = &args[1];
        println!("Executing Lua file: {}", lua_file);
        let script = fs::read_to_string(lua_file)?;
        lua.load(&script).exec()?;
        return Ok(());
    }

    println!("ExtensibleDB Lua console (type .exit to quit)");
    let stdin = io::stdin();

    loop {
        print!("lua> ");
        io::stdout().flush()?;

        let mut line = String::new();
        stdin.read_line(&mut line)?;
        let cmd = line.trim();

        if cmd.is_empty() {
            continue;
        }
        if cmd == ".exit" {
            break;
        }

        match lua.load(cmd).eval::<LuaValue>() {
            Ok(val) => {
                if !matches!(val, LuaValue::Nil) {
                    println!("{:?}", val);
                }
            }
            Err(e) => eprintln!("error: {e}"),
        }
    }
    Ok(())
}

impl Database {
    pub fn open<P: AsRef<Path>>(path: P) -> Result<Self> {
        let storage = sled::open(path).context("failed to open storage")?;
        // Load persisted index metadata so that secondary index trees remain
        // usable after database reopen.
        let meta_tree = storage.open_tree(META_INDEXES_TREE)?;
        let mut idx_map: HashMap<String, Vec<String>> = HashMap::new();
        for kv in meta_tree.iter() {
            let (k, _) = kv?;
            if let Ok(s) = std::str::from_utf8(&k) {
                if let Some((tbl, fld)) = s.split_once(':') {
                    idx_map
                        .entry(tbl.to_string())
                        .or_default()
                        .push(fld.to_string());
                }
            }
        }

        Ok(Self {
            storage,
            indexes: Arc::new(RwLock::new(idx_map)),
            transaction_active: Arc::new(AtomicBool::new(false)),
            pending_ops: Arc::new(Mutex::new(Vec::new())),
        })
    }

    pub fn create_table(&self, name: &str) -> Result<()> {
        self.storage.open_tree(name)?;
        Ok(())
    }

    pub fn create_index(&self, table: &str, field: &str) -> Result<()> {
        self.storage.open_tree(idx_tree_name(table, field))?;

        // Persist metadata for durability
        let meta_tree = self.storage.open_tree(META_INDEXES_TREE)?;
        let meta_key = format!("{}:{}", table, field);
        meta_tree.insert(meta_key.as_bytes(), &[])?;

        // Register the index
        let mut indexes = self.indexes.write();
        indexes
            .entry(table.to_string())
            .or_insert_with(Vec::new)
            .push(field.to_string());

        Ok(())
    }

    pub fn insert_row(&self, table: &str, row: Value) -> Result<u64> {
        let id = self.storage.generate_id()?;
        let key = id.to_be_bytes().to_vec();
        let data = serde_json::to_vec(&row)?;

        if self.transaction_active.load(Ordering::SeqCst) {
            // Buffer operation
            let mut buf = self.pending_ops.lock();
            buf.push(PendingOp::InsertRow {
                table: table.to_string(),
                key: key.clone(),
                data: data.clone(),
            });
        } else {
            // Immediate apply
            let tree = self.storage.open_tree(table)?;
            tree.insert(&key, data.clone())?;
        }

        // Handle indexes (either buffer or immediate)
        let indexes = self.indexes.read();
        if let Some(table_indexes) = indexes.get(table) {
            for field in table_indexes {
                if let Some(val) = row.get(field) {
                    let val_bytes = serde_json::to_vec(val)?;
                    let mut idx_key = Vec::with_capacity(4 + val_bytes.len() + key.len());
                    idx_key.extend_from_slice(&(val_bytes.len() as u32).to_be_bytes());
                    idx_key.extend_from_slice(&val_bytes);
                    idx_key.extend_from_slice(&key);

                    if self.transaction_active.load(Ordering::SeqCst) {
                        let mut buf = self.pending_ops.lock();
                        buf.push(PendingOp::IndexInsert {
                            table: table.to_string(),
                            field: field.to_string(),
                            idx_key,
                        });
                    } else {
                        let idx_tree = self.storage.open_tree(idx_tree_name(table, field))?;
                        idx_tree.insert(idx_key, &[])?;
                    }
                }
            }
        }

        Ok(id)
    }

    pub fn insert_rows(&self, table: &str, rows: Vec<Value>) -> Result<Vec<u64>> {
        let tree = self.storage.open_tree(table)?;
        let mut ids = Vec::with_capacity(rows.len());

        // Get registered indexes for this table
        let indexes = self.indexes.read();
        let table_indexes = indexes.get(table).cloned().unwrap_or_default();
        drop(indexes);

        let in_tx = self.transaction_active.load(Ordering::SeqCst);

        if table_indexes.is_empty() && !in_tx {
            // No indexes and not in transaction, can use simple batch
            let mut batch = sled::Batch::default();
            for row in rows {
                let id = self.storage.generate_id()?;
                ids.push(id);
                let key = id.to_be_bytes().to_vec();
                let data = serde_json::to_vec(&row)?;
                batch.insert(key, data);
            }
            tree.apply_batch(batch)?;
        } else {
            // Either there are indexes or we are in a transaction; fall back to row-by-row
            for row in rows {
                let id = self.insert_row(table, row)?;
                ids.push(id);
            }
        }

        Ok(ids)
    }

    pub fn select_all(&self, table: &str) -> Result<Vec<Value>> {
        let tree = self.storage.open_tree(table)?;
        let mut rows = Vec::new();
        for kv in tree.iter() {
            let (_k, v) = kv?;
            let row: Value = serde_json::from_slice(&v)?;
            rows.push(row);
        }
        Ok(rows)
    }

    pub fn select_by_index(&self, table: &str, field: &str, value: &Value) -> Result<Vec<Value>> {
        let table_tree = self.storage.open_tree(table)?;
        let idx_tree = self.storage.open_tree(idx_tree_name(table, field))?;

        // Query using length-prefixed value
        let val_bytes = serde_json::to_vec(value)?;
        let mut prefix = Vec::with_capacity(4 + val_bytes.len());
        prefix.extend_from_slice(&(val_bytes.len() as u32).to_be_bytes());
        prefix.extend_from_slice(&val_bytes);

        let mut results = Vec::new();
        for item in idx_tree.scan_prefix(prefix) {
            let (comp_key, _) = item?;
            // last 8 bytes are the id
            if comp_key.len() >= 8 {
                let id_bytes = &comp_key[comp_key.len() - 8..];
                if let Some(v) = table_tree.get(id_bytes)? {
                    let row: Value = serde_json::from_slice(&v)?;
                    results.push(row);
                }
            }
        }
        Ok(results)
    }

    // Simple transaction placeholders - sled doesn't have explicit begin/commit
    // These are here for API compatibility but don't do much yet
    pub fn begin_transaction(&self) -> Result<()> {
        // Flip the flag so operations are buffered.
        self.transaction_active.store(true, Ordering::SeqCst);
        let mut buf = self.pending_ops.lock();
        buf.clear();
        Ok(())
    }

    pub fn commit_transaction(&self) -> Result<()> {
        if !self.transaction_active.swap(false, Ordering::SeqCst) {
            // no active tx
            return Ok(());
        }

        // Drain buffered operations and apply
        let mut buf = self.pending_ops.lock();
        for op in buf.iter() {
            match op {
                PendingOp::InsertRow { table, key, data } => {
                    let tree = self.storage.open_tree(table)?;
                    tree.insert(key, data)?;
                }
                PendingOp::IndexInsert {
                    table,
                    field,
                    idx_key,
                } => {
                    let idx_tree = self.storage.open_tree(idx_tree_name(table, field))?;
                    idx_tree.insert(idx_key, &[])?;
                }
            }
        }
        buf.clear();
        self.storage.flush()?;
        Ok(())
    }

    pub fn rollback_transaction(&self) -> Result<()> {
        // Simply discard buffered operations
        if self.transaction_active.swap(false, Ordering::SeqCst) {
            let mut buf = self.pending_ops.lock();
            buf.clear();
        }
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::tempdir;

    fn create_test_db() -> Result<Database> {
        let dir = tempdir()?;
        Database::open(dir.path().join("test.db"))
    }

    #[test]
    fn test_basic_operations() -> Result<()> {
        let db = create_test_db()?;

        // Test table creation
        db.create_table("test_table")?;

        // Test insert
        let _id = db.insert_row(
            "test_table",
            serde_json::json!({
                "name": "Alice",
                "age": 30
            }),
        )?;

        // Test select all
        let rows = db.select_all("test_table")?;
        assert_eq!(rows.len(), 1);
        assert_eq!(rows[0]["name"], "Alice");
        assert_eq!(rows[0]["age"], 30);

        Ok(())
    }

    #[test]
    fn test_bulk_operations() -> Result<()> {
        let db = create_test_db()?;
        db.create_table("bulk_test")?;

        let test_data = vec![
            serde_json::json!({"name": "User1", "score": 100}),
            serde_json::json!({"name": "User2", "score": 200}),
            serde_json::json!({"name": "User3", "score": 300}),
        ];

        let ids = db.insert_rows("bulk_test", test_data)?;
        assert_eq!(ids.len(), 3);

        let rows = db.select_all("bulk_test")?;
        assert_eq!(rows.len(), 3);

        Ok(())
    }

    #[test]
    fn test_secondary_indexes() -> Result<()> {
        let db = create_test_db()?;
        db.create_table("index_test")?;
        db.create_index("index_test", "category")?;

        // Insert test data
        db.insert_row(
            "index_test",
            serde_json::json!({
                "name": "Item1",
                "category": "A"
            }),
        )?;

        db.insert_row(
            "index_test",
            serde_json::json!({
                "name": "Item2",
                "category": "B"
            }),
        )?;

        db.insert_row(
            "index_test",
            serde_json::json!({
                "name": "Item3",
                "category": "A"
            }),
        )?;

        // Test index query
        let results = db.select_by_index("index_test", "category", &serde_json::json!("A"))?;
        assert_eq!(results.len(), 2);

        let results_b = db.select_by_index("index_test", "category", &serde_json::json!("B"))?;
        assert_eq!(results_b.len(), 1);

        Ok(())
    }

    #[test]
    fn test_transactions() -> Result<()> {
        let db = create_test_db()?;
        db.create_table("tx_test")?;

        // Test transaction methods (they're no-ops in current implementation)
        db.begin_transaction()?;
        db.insert_row("tx_test", serde_json::json!({"test": "data"}))?;
        db.commit_transaction()?;

        let rows = db.select_all("tx_test")?;
        assert_eq!(rows.len(), 1);

        Ok(())
    }
}
