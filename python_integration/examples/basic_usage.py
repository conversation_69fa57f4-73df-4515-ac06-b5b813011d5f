"""
Basic usage examples for ExtensibleDB Python integration.

This script demonstrates fundamental operations like table creation,
data insertion, and querying.
"""

import asyncio
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from db_wrapper import ExtensibleDB, ExtensibleDBError
from models import User, Product, model_to_dict


async def basic_operations_example():
    """Demonstrate basic database operations"""
    print("🔧 Basic Operations Example")
    print("=" * 40)
    
    try:
        # Initialize database
        db = ExtensibleDB()
        print("✅ Database initialized")
        
        # Create a table
        await db.create_table("users")
        print("✅ Created 'users' table")
        
        # Create an index for fast queries
        await db.create_index("users", "email")
        print("✅ Created index on 'email' field")
        
        # Insert a single record
        user_data = {
            "name": "<PERSON> Johnson",
            "email": "<EMAIL>",
            "age": 30,
            "department": "Engineering"
        }
        
        user_id = await db.insert_row("users", user_data)
        print(f"✅ Inserted user with ID: {user_id}")
        
        # Query all records
        all_users = await db.select_all("users")
        print(f"📊 Found {len(all_users)} users:")
        for user in all_users:
            print(f"  - {user['name']} ({user['email']})")
        
        # Query by index
        alice_users = await db.select_by_index("users", "email", "<EMAIL>")
        print(f"🔍 Found {len(alice_users)} users with email '<EMAIL>'")
        
    except ExtensibleDBError as e:
        print(f"❌ Database error: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")


async def bulk_operations_example():
    """Demonstrate bulk operations for better performance"""
    print("\n📦 Bulk Operations Example")
    print("=" * 40)
    
    try:
        db = ExtensibleDB()
        
        # Create table and index
        await db.create_table("products")
        await db.create_index("products", "category")
        print("✅ Created 'products' table with category index")
        
        # Prepare bulk data
        products_data = [
            {
                "name": "Laptop Pro",
                "price": 1299.99,
                "category": "electronics",
                "stock": 10
            },
            {
                "name": "Wireless Mouse",
                "price": 29.99,
                "category": "electronics", 
                "stock": 50
            },
            {
                "name": "Office Desk",
                "price": 299.99,
                "category": "furniture",
                "stock": 5
            },
            {
                "name": "Ergonomic Chair",
                "price": 199.99,
                "category": "furniture",
                "stock": 8
            }
        ]
        
        # Bulk insert
        product_ids = await db.insert_rows("products", products_data)
        print(f"✅ Bulk inserted {len(product_ids)} products")
        print(f"📋 Product IDs: {product_ids}")
        
        # Query by category
        electronics = await db.select_by_index("products", "category", "electronics")
        print(f"💻 Electronics products: {len(electronics)}")
        for product in electronics:
            print(f"  - {product['name']}: ${product['price']}")
        
        furniture = await db.select_by_index("products", "category", "furniture")
        print(f"🪑 Furniture products: {len(furniture)}")
        for product in furniture:
            print(f"  - {product['name']}: ${product['price']}")
        
    except ExtensibleDBError as e:
        print(f"❌ Database error: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")


async def pydantic_models_example():
    """Demonstrate using Pydantic models for type safety"""
    print("\n🔒 Pydantic Models Example")
    print("=" * 40)
    
    try:
        db = ExtensibleDB()
        
        # Create table
        await db.create_table("validated_users")
        await db.create_index("validated_users", "department")
        print("✅ Created 'validated_users' table")
        
        # Create users using Pydantic models
        users = [
            User(name="John Doe", email="<EMAIL>", age=28, department="Engineering"),
            User(name="Jane Smith", email="<EMAIL>", age=32, department="Marketing"),
            User(name="Bob Wilson", email="<EMAIL>", age=25, department="Engineering")
        ]
        
        # Convert models to dictionaries for database storage
        users_data = [model_to_dict(user) for user in users]
        
        # Insert with validation
        user_ids = await db.insert_rows("validated_users", users_data)
        print(f"✅ Inserted {len(user_ids)} validated users")
        
        # Query engineers
        engineers = await db.select_by_index("validated_users", "department", "Engineering")
        print(f"👨‍💻 Engineering team: {len(engineers)} members")
        for engineer in engineers:
            print(f"  - {engineer['name']} (age {engineer['age']})")
        
        # Demonstrate validation (this would fail in real usage)
        print("\n🧪 Validation example:")
        try:
            invalid_user = User(
                name="",  # Invalid: empty name
                email="invalid-email",  # Invalid: bad email format
                age=-1  # Invalid: negative age
            )
            print("❌ This should have failed validation!")
        except Exception as e:
            print(f"✅ Validation caught error: {type(e).__name__}")
        
    except ExtensibleDBError as e:
        print(f"❌ Database error: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")


async def transaction_example():
    """Demonstrate transaction usage"""
    print("\n💳 Transaction Example")
    print("=" * 40)
    
    try:
        db = ExtensibleDB()
        
        # Create table
        await db.create_table("accounts")
        print("✅ Created 'accounts' table")
        
        # Begin transaction
        await db.begin_transaction()
        print("🔄 Transaction started")
        
        # Perform operations within transaction
        account1_id = await db.insert_row("accounts", {
            "name": "Alice Account",
            "balance": 1000.00,
            "type": "checking"
        })
        
        account2_id = await db.insert_row("accounts", {
            "name": "Bob Account", 
            "balance": 500.00,
            "type": "savings"
        })
        
        print(f"➕ Added accounts: {account1_id}, {account2_id}")
        
        # Commit transaction
        await db.commit_transaction()
        print("✅ Transaction committed")
        
        # Verify data
        accounts = await db.select_all("accounts")
        print(f"📊 Total accounts: {len(accounts)}")
        for account in accounts:
            print(f"  - {account['name']}: ${account['balance']}")
        
    except ExtensibleDBError as e:
        print(f"❌ Database error: {e}")
        # In case of error, rollback
        try:
            await db.rollback_transaction()
            print("🔄 Transaction rolled back")
        except:
            pass
    except Exception as e:
        print(f"❌ Unexpected error: {e}")


async def error_handling_example():
    """Demonstrate error handling best practices"""
    print("\n🚨 Error Handling Example")
    print("=" * 40)
    
    try:
        db = ExtensibleDB()
        
        # Example 1: Handling table creation errors
        try:
            await db.create_table("test_table")
            print("✅ Table created successfully")
        except ExtensibleDBError as e:
            print(f"⚠️  Table creation error: {e}")
        
        # Example 2: Handling invalid data
        try:
            # This might fail if table doesn't exist
            await db.insert_row("nonexistent_table", {"data": "test"})
        except ExtensibleDBError as e:
            print(f"⚠️  Insert error (expected): {e}")
        
        # Example 3: Handling query errors
        try:
            await db.select_by_index("nonexistent_table", "field", "value")
        except ExtensibleDBError as e:
            print(f"⚠️  Query error (expected): {e}")
        
        # Example 4: Health check
        is_healthy = await db.health_check()
        if is_healthy:
            print("✅ Database health check passed")
        else:
            print("❌ Database health check failed")
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")


async def main():
    """Run all basic examples"""
    print("🚀 ExtensibleDB Basic Usage Examples")
    print("=" * 50)
    
    await basic_operations_example()
    await bulk_operations_example()
    await pydantic_models_example()
    await transaction_example()
    await error_handling_example()
    
    print("\n🎉 All examples completed!")
    print("\n💡 Next steps:")
    print("  - Try the advanced examples: python examples/advanced_usage.py")
    print("  - Run performance tests: python examples/performance_demo.py")
    print("  - Start the FastAPI server: python src/main.py")


if __name__ == "__main__":
    asyncio.run(main())
