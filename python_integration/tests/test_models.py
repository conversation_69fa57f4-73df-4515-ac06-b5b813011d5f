"""
Unit tests for Pydantic models.

These tests verify the validation, serialization, and deserialization
of all Pydantic models used in the ExtensibleDB integration.
"""

import pytest
from datetime import datetime
from pydantic import ValidationError
import json

from src.models import (
    TableCreateRequest, IndexCreateRequest, DocumentInsertRequest,
    BulkInsertRequest, QueryByIndexRequest, TableQueryRequest,
    SuccessResponse, ErrorResponse, InsertResponse, BulkInsertResponse,
    QueryResponse, TransactionResponse, HealthResponse,
    User, Product, Order, model_to_dict, dict_to_model, validate_json_serializable
)


@pytest.mark.unit
class TestRequestModels:
    """Test request models validation"""
    
    def test_table_create_request_valid(self):
        """Test valid table creation request"""
        request = TableCreateRequest(name="valid_table_name")
        assert request.name == "valid_table_name"
    
    def test_table_create_request_invalid_name(self):
        """Test invalid table names"""
        # Empty name
        with pytest.raises(ValidationError):
            TableCreateRequest(name="")
        
        # Name starting with number
        with pytest.raises(ValidationError):
            TableCreateRequest(name="123invalid")
        
        # Name with special characters
        with pytest.raises(ValidationError):
            TableCreateRequest(name="invalid-name")
        
        # Name too long
        with pytest.raises(ValidationError):
            TableCreateRequest(name="a" * 101)
    
    def test_index_create_request_valid(self):
        """Test valid index creation request"""
        request = IndexCreateRequest(table_name="test_table", field_name="test_field")
        assert request.table_name == "test_table"
        assert request.field_name == "test_field"
    
    def test_index_create_request_invalid(self):
        """Test invalid index creation requests"""
        with pytest.raises(ValidationError):
            IndexCreateRequest(table_name="", field_name="field")
        
        with pytest.raises(ValidationError):
            IndexCreateRequest(table_name="table", field_name="123field")
    
    def test_document_insert_request_valid(self):
        """Test valid document insertion request"""
        data = {"name": "test", "value": 42, "active": True}
        request = DocumentInsertRequest(table_name="test_table", data=data)
        assert request.table_name == "test_table"
        assert request.data == data
    
    def test_document_insert_request_invalid(self):
        """Test invalid document insertion requests"""
        # Empty data
        with pytest.raises(ValidationError):
            DocumentInsertRequest(table_name="test_table", data={})
        
        # Invalid table name
        with pytest.raises(ValidationError):
            DocumentInsertRequest(table_name="123invalid", data={"test": "data"})
    
    def test_bulk_insert_request_valid(self):
        """Test valid bulk insertion request"""
        data = [{"name": "test1"}, {"name": "test2"}]
        request = BulkInsertRequest(table_name="test_table", data=data)
        assert request.table_name == "test_table"
        assert request.data == data
    
    def test_bulk_insert_request_invalid(self):
        """Test invalid bulk insertion requests"""
        # Empty data list
        with pytest.raises(ValidationError):
            BulkInsertRequest(table_name="test_table", data=[])
        
        # Too many items
        data = [{"name": f"test{i}"} for i in range(10001)]
        with pytest.raises(ValidationError):
            BulkInsertRequest(table_name="test_table", data=data)
        
        # Empty item in list
        with pytest.raises(ValidationError):
            BulkInsertRequest(table_name="test_table", data=[{"name": "test"}, {}])
    
    def test_query_by_index_request_valid(self):
        """Test valid index query request"""
        request = QueryByIndexRequest(
            table_name="test_table",
            field_name="test_field",
            value="test_value"
        )
        assert request.table_name == "test_table"
        assert request.field_name == "test_field"
        assert request.value == "test_value"
    
    def test_query_by_index_request_different_value_types(self):
        """Test index query with different value types"""
        # String value
        request = QueryByIndexRequest(
            table_name="test_table",
            field_name="name",
            value="test"
        )
        assert request.value == "test"
        
        # Integer value
        request = QueryByIndexRequest(
            table_name="test_table",
            field_name="age",
            value=25
        )
        assert request.value == 25
        
        # Float value
        request = QueryByIndexRequest(
            table_name="test_table",
            field_name="price",
            value=99.99
        )
        assert request.value == 99.99
        
        # Boolean value
        request = QueryByIndexRequest(
            table_name="test_table",
            field_name="active",
            value=True
        )
        assert request.value is True
    
    def test_table_query_request_valid(self):
        """Test valid table query request"""
        request = TableQueryRequest(table_name="test_table")
        assert request.table_name == "test_table"
        assert request.limit is None
        assert request.offset is None
        
        # With pagination
        request = TableQueryRequest(table_name="test_table", limit=10, offset=5)
        assert request.limit == 10
        assert request.offset == 5
    
    def test_table_query_request_invalid_pagination(self):
        """Test invalid pagination parameters"""
        # Negative limit
        with pytest.raises(ValidationError):
            TableQueryRequest(table_name="test_table", limit=-1)
        
        # Limit too large
        with pytest.raises(ValidationError):
            TableQueryRequest(table_name="test_table", limit=10001)
        
        # Negative offset
        with pytest.raises(ValidationError):
            TableQueryRequest(table_name="test_table", offset=-1)


@pytest.mark.unit
class TestResponseModels:
    """Test response models"""
    
    def test_success_response(self):
        """Test success response model"""
        response = SuccessResponse(message="Operation successful")
        assert response.success is True
        assert response.message == "Operation successful"
        assert isinstance(response.timestamp, datetime)
    
    def test_error_response(self):
        """Test error response model"""
        response = ErrorResponse(error="Something went wrong")
        assert response.success is False
        assert response.error == "Something went wrong"
        assert response.details is None
        assert isinstance(response.timestamp, datetime)
        
        # With details
        response = ErrorResponse(error="Error", details="More info")
        assert response.details == "More info"
    
    def test_insert_response(self):
        """Test insert response model"""
        response = InsertResponse(id=123)
        assert response.success is True
        assert response.id == 123
        assert response.message == "Document inserted successfully"
        assert isinstance(response.timestamp, datetime)
    
    def test_bulk_insert_response(self):
        """Test bulk insert response model"""
        ids = [1, 2, 3]
        response = BulkInsertResponse(ids=ids, count=3)
        assert response.success is True
        assert response.ids == ids
        assert response.count == 3
        assert response.message == "Documents inserted successfully"
    
    def test_bulk_insert_response_count_mismatch(self):
        """Test bulk insert response with count mismatch"""
        with pytest.raises(ValidationError):
            BulkInsertResponse(ids=[1, 2, 3], count=2)
    
    def test_query_response(self):
        """Test query response model"""
        data = [{"id": 1, "name": "test"}]
        response = QueryResponse(data=data, count=1)
        assert response.success is True
        assert response.data == data
        assert response.count == 1
        assert response.total is None
        assert response.message == "Query executed successfully"
    
    def test_query_response_with_total(self):
        """Test query response with total count"""
        data = [{"id": 1, "name": "test"}]
        response = QueryResponse(data=data, count=1, total=10)
        assert response.total == 10
    
    def test_transaction_response(self):
        """Test transaction response model"""
        response = TransactionResponse(operation="commit", message="Transaction committed")
        assert response.success is True
        assert response.operation == "commit"
        assert response.message == "Transaction committed"
    
    def test_health_response(self):
        """Test health response model"""
        response = HealthResponse()
        assert response.success is True
        assert response.status == "healthy"
        assert response.database_responsive is True
        
        # Unhealthy state
        response = HealthResponse(
            status="degraded",
            database_responsive=False
        )
        assert response.status == "degraded"
        assert response.database_responsive is False


@pytest.mark.unit
class TestDomainModels:
    """Test domain-specific models"""
    
    def test_user_model_valid(self):
        """Test valid user model"""
        user = User(
            name="John Doe",
            email="<EMAIL>",
            age=30,
            department="Engineering"
        )
        assert user.name == "John Doe"
        assert user.email == "<EMAIL>"
        assert user.age == 30
        assert user.department == "Engineering"
        assert user.active is True
        assert isinstance(user.created_at, datetime)
    
    def test_user_model_invalid_email(self):
        """Test user model with invalid email"""
        with pytest.raises(ValidationError):
            User(name="John", email="invalid-email", age=30)
    
    def test_user_model_invalid_age(self):
        """Test user model with invalid age"""
        with pytest.raises(ValidationError):
            User(name="John", email="<EMAIL>", age=-1)
        
        with pytest.raises(ValidationError):
            User(name="John", email="<EMAIL>", age=200)
    
    def test_product_model_valid(self):
        """Test valid product model"""
        product = Product(
            name="Test Product",
            price=99.99,
            category="electronics",
            stock=10
        )
        assert product.name == "Test Product"
        assert product.price == 99.99
        assert product.category == "electronics"
        assert product.stock == 10
        assert product.active is True
        assert isinstance(product.created_at, datetime)
    
    def test_product_model_invalid_price(self):
        """Test product model with invalid price"""
        with pytest.raises(ValidationError):
            Product(name="Test", price=0, category="test", stock=1)
        
        with pytest.raises(ValidationError):
            Product(name="Test", price=-10, category="test", stock=1)
    
    def test_product_model_invalid_stock(self):
        """Test product model with invalid stock"""
        with pytest.raises(ValidationError):
            Product(name="Test", price=10, category="test", stock=-1)
    
    def test_order_model_valid(self):
        """Test valid order model"""
        order = Order(
            user_id=1,
            product_id=2,
            quantity=3,
            total_price=299.97
        )
        assert order.user_id == 1
        assert order.product_id == 2
        assert order.quantity == 3
        assert order.total_price == 299.97
        assert order.status == "pending"
        assert isinstance(order.created_at, datetime)
    
    def test_order_model_invalid_status(self):
        """Test order model with invalid status"""
        with pytest.raises(ValidationError):
            Order(
                user_id=1,
                product_id=2,
                quantity=1,
                total_price=10,
                status="invalid_status"
            )
    
    def test_order_model_invalid_ids(self):
        """Test order model with invalid IDs"""
        with pytest.raises(ValidationError):
            Order(user_id=0, product_id=1, quantity=1, total_price=10)
        
        with pytest.raises(ValidationError):
            Order(user_id=1, product_id=0, quantity=1, total_price=10)


@pytest.mark.unit
class TestUtilityFunctions:
    """Test utility functions"""
    
    def test_model_to_dict(self):
        """Test model to dictionary conversion"""
        user = User(name="Test", email="<EMAIL>", age=25)
        user_dict = model_to_dict(user)
        
        assert isinstance(user_dict, dict)
        assert user_dict["name"] == "Test"
        assert user_dict["email"] == "<EMAIL>"
        assert user_dict["age"] == 25
        assert "created_at" in user_dict
    
    def test_dict_to_model(self):
        """Test dictionary to model conversion"""
        user_dict = {
            "name": "Test",
            "email": "<EMAIL>",
            "age": 25
        }
        user = dict_to_model(user_dict, User)
        
        assert isinstance(user, User)
        assert user.name == "Test"
        assert user.email == "<EMAIL>"
        assert user.age == 25
    
    def test_validate_json_serializable_valid(self):
        """Test JSON serializable validation with valid data"""
        valid_data = {
            "string": "test",
            "number": 42,
            "float": 3.14,
            "boolean": True,
            "null": None,
            "list": [1, 2, 3],
            "nested": {"key": "value"}
        }
        assert validate_json_serializable(valid_data) is True
    
    def test_validate_json_serializable_invalid(self):
        """Test JSON serializable validation with invalid data"""
        # Function is not JSON serializable
        invalid_data = {"function": lambda x: x}
        assert validate_json_serializable(invalid_data) is False
        
        # Set is not JSON serializable
        invalid_data = {"set": {1, 2, 3}}
        assert validate_json_serializable(invalid_data) is False


@pytest.mark.unit
class TestModelSerialization:
    """Test model serialization and deserialization"""
    
    def test_user_json_serialization(self):
        """Test user model JSON serialization"""
        user = User(name="Test", email="<EMAIL>", age=25)
        json_str = user.json()
        
        # Should be valid JSON
        parsed = json.loads(json_str)
        assert parsed["name"] == "Test"
        assert parsed["email"] == "<EMAIL>"
        assert parsed["age"] == 25
    
    def test_product_json_serialization(self):
        """Test product model JSON serialization"""
        product = Product(
            name="Test Product",
            price=99.99,
            category="test",
            stock=10,
            tags=["tag1", "tag2"]
        )
        json_str = product.json()
        
        parsed = json.loads(json_str)
        assert parsed["name"] == "Test Product"
        assert parsed["price"] == 99.99
        assert parsed["tags"] == ["tag1", "tag2"]
    
    def test_datetime_serialization(self):
        """Test datetime serialization in models"""
        user = User(name="Test", email="<EMAIL>", age=25)
        user_dict = model_to_dict(user)
        
        # created_at should be serialized as ISO format string
        assert isinstance(user_dict["created_at"], str)
        # Should be parseable back to datetime
        datetime.fromisoformat(user_dict["created_at"].replace('Z', '+00:00'))
