"""
End-to-end integration tests for ExtensibleDB Python integration.

These tests verify the complete workflow from API requests to database operations.
"""

import pytest
import asyncio
from unittest.mock import patch, AsyncMock
import json

from src.db_wrapper import ExtensibleDB, ExtensibleDBError
from src.models import User, Product, Order, model_to_dict


@pytest.mark.integration
class TestCompleteWorkflows:
    """Test complete end-to-end workflows"""
    
    @pytest.fixture
    async def workflow_db(self, temp_db_dir):
        """Create a database instance for workflow testing"""
        with patch.object(ExtensibleDB, '_find_binary') as mock_find:
            mock_find.return_value = "/fake/binary"
            with patch.object(ExtensibleDB, '_validate_setup'):
                db = ExtensibleDB(str(temp_db_dir / "workflow.db"))
                yield db
    
    @pytest.mark.asyncio
    async def test_user_management_workflow(self, workflow_db):
        """Test complete user management workflow"""
        # Mock script execution responses
        responses = {
            'create_table': "Table 'users' created successfully",
            'create_index_email': "Index created on users.email",
            'create_index_dept': "Index created on users.department",
            'insert_user': "1",
            'select_all': json.dumps([{
                "id": 1,
                "name": "<PERSON> Doe",
                "email": "<EMAIL>",
                "age": 30,
                "department": "Engineering"
            }]),
            'select_by_email': json.dumps([{
                "id": 1,
                "name": "<PERSON>e",
                "email": "<EMAIL>",
                "age": 30,
                "department": "Engineering"
            }])
        }
        
        call_count = 0
        def mock_script_exec(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                return responses['create_table']
            elif call_count == 2:
                return responses['create_index_email']
            elif call_count == 3:
                return responses['create_index_dept']
            elif call_count == 4:
                return responses['insert_user']
            elif call_count == 5:
                return responses['select_all']
            elif call_count == 6:
                return responses['select_by_email']
            else:
                return "1"
        
        with patch.object(workflow_db, '_execute_lua_script', side_effect=mock_script_exec):
            # 1. Create users table
            await workflow_db.create_table("users")
            
            # 2. Create indexes
            await workflow_db.create_index("users", "email")
            await workflow_db.create_index("users", "department")
            
            # 3. Insert a user
            user_data = {
                "name": "John Doe",
                "email": "<EMAIL>",
                "age": 30,
                "department": "Engineering"
            }
            user_id = await workflow_db.insert_row("users", user_data)
            assert user_id == 1
            
            # 4. Query all users
            all_users = await workflow_db.select_all("users")
            assert len(all_users) == 1
            assert all_users[0]["name"] == "John Doe"
            
            # 5. Query user by email
            users_by_email = await workflow_db.select_by_index("users", "email", "<EMAIL>")
            assert len(users_by_email) == 1
            assert users_by_email[0]["email"] == "<EMAIL>"
    
    @pytest.mark.asyncio
    async def test_ecommerce_workflow(self, workflow_db):
        """Test complete e-commerce workflow"""
        # Mock responses for e-commerce workflow
        responses = {
            'users': [{"id": 1, "name": "Alice", "email": "<EMAIL>"}],
            'products': [{"id": 1, "name": "Laptop", "price": 999.99, "stock": 5}],
            'orders': [{"id": 1, "user_id": 1, "product_id": 1, "quantity": 1, "total_price": 999.99}]
        }
        
        call_sequence = [
            "Table 'users' created successfully",
            "Index created on users.email",
            "1",  # Insert user
            "Table 'products' created successfully", 
            "Index created on products.category",
            "1",  # Insert product
            "Table 'orders' created successfully",
            "Index created on orders.user_id",
            "1",  # Insert order
            json.dumps(responses['users']),  # Get user
            json.dumps(responses['products']),  # Get product
            json.dumps(responses['orders'])  # Get order
        ]
        
        call_count = 0
        def mock_script_exec(*args, **kwargs):
            nonlocal call_count
            result = call_sequence[call_count] if call_count < len(call_sequence) else "1"
            call_count += 1
            return result
        
        with patch.object(workflow_db, '_execute_lua_script', side_effect=mock_script_exec):
            # 1. Set up users
            await workflow_db.create_table("users")
            await workflow_db.create_index("users", "email")
            user_id = await workflow_db.insert_row("users", {
                "name": "Alice",
                "email": "<EMAIL>",
                "age": 28
            })
            
            # 2. Set up products
            await workflow_db.create_table("products")
            await workflow_db.create_index("products", "category")
            product_id = await workflow_db.insert_row("products", {
                "name": "Laptop",
                "price": 999.99,
                "category": "electronics",
                "stock": 5
            })
            
            # 3. Create order
            await workflow_db.create_table("orders")
            await workflow_db.create_index("orders", "user_id")
            order_id = await workflow_db.insert_row("orders", {
                "user_id": user_id,
                "product_id": product_id,
                "quantity": 1,
                "total_price": 999.99,
                "status": "pending"
            })
            
            # 4. Verify data integrity
            users = await workflow_db.select_all("users")
            products = await workflow_db.select_all("products")
            orders = await workflow_db.select_all("orders")
            
            assert len(users) == 1
            assert len(products) == 1
            assert len(orders) == 1
            
            # Verify relationships
            assert orders[0]["user_id"] == users[0]["id"]
            assert orders[0]["product_id"] == products[0]["id"]
    
    @pytest.mark.asyncio
    async def test_transaction_workflow(self, workflow_db):
        """Test transaction workflow"""
        transaction_responses = [
            "",  # begin_transaction
            "1",  # insert operation 1
            "2",  # insert operation 2
            "",  # commit_transaction
            json.dumps([
                {"id": 1, "name": "Item 1"},
                {"id": 2, "name": "Item 2"}
            ])  # select_all
        ]
        
        call_count = 0
        def mock_script_exec(*args, **kwargs):
            nonlocal call_count
            result = transaction_responses[call_count] if call_count < len(transaction_responses) else ""
            call_count += 1
            return result
        
        with patch.object(workflow_db, '_execute_lua_script', side_effect=mock_script_exec):
            # 1. Begin transaction
            await workflow_db.begin_transaction()
            
            # 2. Perform multiple operations
            id1 = await workflow_db.insert_row("test_table", {"name": "Item 1"})
            id2 = await workflow_db.insert_row("test_table", {"name": "Item 2"})
            
            # 3. Commit transaction
            await workflow_db.commit_transaction()
            
            # 4. Verify results
            results = await workflow_db.select_all("test_table")
            
            assert id1 == 1
            assert id2 == 2
            assert len(results) == 2


@pytest.mark.integration
class TestAPIIntegration:
    """Test API integration with database operations"""
    
    def test_complete_api_workflow(self, test_client, override_get_db):
        """Test complete API workflow"""
        # Configure mock responses
        override_get_db.create_table.return_value = True
        override_get_db.create_index.return_value = True
        override_get_db.insert_row.return_value = 1
        override_get_db.select_all.return_value = [{
            "id": 1,
            "name": "Test User",
            "email": "<EMAIL>",
            "age": 25,
            "department": "Testing"
        }]
        override_get_db.select_by_index.return_value = [{
            "id": 1,
            "name": "Test User",
            "email": "<EMAIL>",
            "age": 25,
            "department": "Testing"
        }]
        
        # 1. Create table
        response = test_client.post("/tables", json={"name": "users"})
        assert response.status_code == 200
        
        # 2. Create index
        response = test_client.post(
            "/tables/users/indexes",
            json={"table_name": "users", "field_name": "email"}
        )
        assert response.status_code == 200
        
        # 3. Insert user
        user_data = {
            "name": "Test User",
            "email": "<EMAIL>",
            "age": 25,
            "department": "Testing"
        }
        response = test_client.post("/users", json=user_data)
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == 1
        
        # 4. Get all users
        response = test_client.get("/users")
        assert response.status_code == 200
        data = response.json()
        assert len(data["data"]) == 1
        assert data["data"][0]["name"] == "Test User"
        
        # 5. Query by department
        response = test_client.get("/users/by-department/Testing")
        assert response.status_code == 200
        data = response.json()
        assert len(data["data"]) == 1
        assert data["data"][0]["department"] == "Testing"
    
    def test_error_propagation(self, test_client, override_get_db):
        """Test error propagation from database to API"""
        # Configure database to raise error
        override_get_db.create_table.side_effect = ExtensibleDBError("Database connection failed")
        
        response = test_client.post("/tables", json={"name": "test_table"})
        assert response.status_code == 400
        
        data = response.json()
        assert data["success"] is False
        assert "Database connection failed" in data["error"]
    
    def test_validation_integration(self, test_client, override_get_db):
        """Test validation integration between API and models"""
        # Test invalid user data
        invalid_user = {
            "name": "",  # Invalid: empty name
            "email": "invalid-email",  # Invalid: bad email format
            "age": -1  # Invalid: negative age
        }
        
        response = test_client.post("/users", json=invalid_user)
        assert response.status_code == 422  # Validation error
        
        # Database should not be called for invalid data
        override_get_db.insert_row.assert_not_called()


@pytest.mark.integration
class TestDataConsistency:
    """Test data consistency across operations"""
    
    @pytest.mark.asyncio
    async def test_bulk_operation_consistency(self, temp_db_dir):
        """Test consistency of bulk operations"""
        with patch.object(ExtensibleDB, '_find_binary') as mock_find:
            mock_find.return_value = "/fake/binary"
            with patch.object(ExtensibleDB, '_validate_setup'):
                db = ExtensibleDB(str(temp_db_dir / "consistency.db"))
                
                # Test data
                users = [
                    {"name": f"User {i}", "email": f"user{i}@example.com", "age": 20 + i}
                    for i in range(100)
                ]
                
                with patch.object(db, '_execute_lua_script') as mock_exec:
                    # Mock bulk insert response
                    ids = '\n'.join(str(i) for i in range(1, 101))
                    mock_exec.return_value = ids
                    
                    # Perform bulk insert
                    result_ids = await db.insert_rows("users", users)
                    
                    # Verify consistency
                    assert len(result_ids) == len(users)
                    assert all(isinstance(id, int) for id in result_ids)
                    assert result_ids == list(range(1, 101))
    
    @pytest.mark.asyncio
    async def test_index_consistency(self, temp_db_dir):
        """Test index consistency with data operations"""
        with patch.object(ExtensibleDB, '_find_binary') as mock_find:
            mock_find.return_value = "/fake/binary"
            with patch.object(ExtensibleDB, '_validate_setup'):
                db = ExtensibleDB(str(temp_db_dir / "index_consistency.db"))
                
                responses = [
                    "Table 'users' created successfully",
                    "Index created on users.department",
                    "1",  # Insert user 1
                    "2",  # Insert user 2
                    json.dumps([
                        {"id": 1, "name": "Alice", "department": "Engineering"},
                        {"id": 2, "name": "Bob", "department": "Engineering"}
                    ])
                ]
                
                call_count = 0
                def mock_script_exec(*args, **kwargs):
                    nonlocal call_count
                    result = responses[call_count] if call_count < len(responses) else "1"
                    call_count += 1
                    return result
                
                with patch.object(db, '_execute_lua_script', side_effect=mock_script_exec):
                    # Create table and index
                    await db.create_table("users")
                    await db.create_index("users", "department")
                    
                    # Insert users with same department
                    await db.insert_row("users", {
                        "name": "Alice",
                        "department": "Engineering"
                    })
                    await db.insert_row("users", {
                        "name": "Bob", 
                        "department": "Engineering"
                    })
                    
                    # Query by index should return both users
                    engineering_users = await db.select_by_index("users", "department", "Engineering")
                    
                    assert len(engineering_users) == 2
                    departments = [user["department"] for user in engineering_users]
                    assert all(dept == "Engineering" for dept in departments)


@pytest.mark.integration
class TestErrorRecovery:
    """Test error recovery and resilience"""
    
    @pytest.mark.asyncio
    async def test_partial_failure_recovery(self, temp_db_dir):
        """Test recovery from partial failures"""
        with patch.object(ExtensibleDB, '_find_binary') as mock_find:
            mock_find.return_value = "/fake/binary"
            with patch.object(ExtensibleDB, '_validate_setup'):
                db = ExtensibleDB(str(temp_db_dir / "recovery.db"))
                
                # Simulate partial failure scenario
                call_count = 0
                def mock_script_exec(*args, **kwargs):
                    nonlocal call_count
                    call_count += 1
                    if call_count == 2:  # Second call fails
                        raise ExtensibleDBError("Simulated failure")
                    return "1"
                
                with patch.object(db, '_execute_lua_script', side_effect=mock_script_exec):
                    # First operation succeeds
                    result1 = await db.insert_row("test_table", {"data": "test1"})
                    assert result1 == 1
                    
                    # Second operation fails
                    with pytest.raises(ExtensibleDBError):
                        await db.insert_row("test_table", {"data": "test2"})
                    
                    # Third operation should work (recovery)
                    result3 = await db.insert_row("test_table", {"data": "test3"})
                    assert result3 == 1
    
    @pytest.mark.asyncio
    async def test_timeout_recovery(self, temp_db_dir):
        """Test recovery from timeout scenarios"""
        with patch.object(ExtensibleDB, '_find_binary') as mock_find:
            mock_find.return_value = "/fake/binary"
            with patch.object(ExtensibleDB, '_validate_setup'):
                db = ExtensibleDB(str(temp_db_dir / "timeout_recovery.db"))
                
                call_count = 0
                async def mock_script_exec(*args, **kwargs):
                    nonlocal call_count
                    call_count += 1
                    if call_count == 1:
                        # First call times out
                        await asyncio.sleep(2)
                        return "1"
                    else:
                        # Subsequent calls are fast
                        return "1"
                
                with patch.object(db, '_execute_lua_script', side_effect=mock_script_exec):
                    # First operation times out
                    with pytest.raises(ExtensibleDBError):
                        await db.insert_row("test_table", {"data": "test"})
                    
                    # Subsequent operations should work
                    result = await db.insert_row("test_table", {"data": "test2"})
                    assert result == 1
