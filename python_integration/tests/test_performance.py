"""
Performance tests for ExtensibleDB Python integration.

These tests verify performance characteristics and identify potential bottlenecks.
"""

import pytest
import asyncio
import time
from unittest.mock import patch, AsyncMock
from concurrent.futures import ThreadPoolExecutor
import statistics

from src.db_wrapper import ExtensibleDB
from conftest import generate_test_users, generate_test_products


@pytest.mark.performance
class TestDatabasePerformance:
    """Test database operation performance"""
    
    @pytest.fixture
    async def perf_db(self, temp_db_dir):
        """Create a database instance for performance testing"""
        with patch.object(ExtensibleDB, '_find_binary') as mock_find:
            mock_find.return_value = "/fake/binary"
            with patch.object(ExtensibleDB, '_validate_setup'):
                with patch.object(ExtensibleDB, '_execute_lua_script') as mock_exec:
                    # Mock fast responses for performance testing
                    mock_exec.return_value = "1"
                    db = ExtensibleDB(str(temp_db_dir / "perf.db"))
                    yield db
    
    @pytest.mark.asyncio
    async def test_single_insert_performance(self, perf_db):
        """Test single insert operation performance"""
        test_data = {"name": "test", "value": 42}
        iterations = 100
        
        # Mock script execution to return incrementing IDs
        with patch.object(perf_db, '_execute_lua_script') as mock_exec:
            mock_exec.side_effect = [str(i) for i in range(1, iterations + 1)]
            
            start_time = time.time()
            
            for i in range(iterations):
                await perf_db.insert_row("test_table", test_data)
            
            end_time = time.time()
            duration = end_time - start_time
            ops_per_second = iterations / duration
            
            print(f"Single insert performance: {ops_per_second:.0f} ops/sec")
            
            # Should be reasonably fast (accounting for subprocess overhead)
            assert ops_per_second > 10  # Very conservative threshold
            assert mock_exec.call_count == iterations
    
    @pytest.mark.asyncio
    async def test_bulk_insert_performance(self, perf_db):
        """Test bulk insert operation performance"""
        batch_sizes = [10, 50, 100, 500]
        
        for batch_size in batch_sizes:
            test_data = generate_test_users(batch_size)
            
            with patch.object(perf_db, '_execute_lua_script') as mock_exec:
                # Mock bulk insert response
                ids = '\n'.join(str(i) for i in range(1, batch_size + 1))
                mock_exec.return_value = ids
                
                start_time = time.time()
                result_ids = await perf_db.insert_rows("test_table", test_data)
                end_time = time.time()
                
                duration = end_time - start_time
                ops_per_second = batch_size / duration
                
                print(f"Bulk insert ({batch_size} records): {ops_per_second:.0f} ops/sec")
                
                assert len(result_ids) == batch_size
                assert ops_per_second > 50  # Should be faster than single inserts
    
    @pytest.mark.asyncio
    async def test_query_performance(self, perf_db):
        """Test query operation performance"""
        # Mock large result set
        large_dataset = generate_test_users(1000)
        
        with patch.object(perf_db, '_execute_lua_script') as mock_exec:
            import json
            mock_exec.return_value = json.dumps(large_dataset)
            
            iterations = 50
            start_time = time.time()
            
            for _ in range(iterations):
                results = await perf_db.select_all("test_table")
                assert len(results) == 1000
            
            end_time = time.time()
            duration = end_time - start_time
            queries_per_second = iterations / duration
            
            print(f"Query performance: {queries_per_second:.0f} queries/sec")
            assert queries_per_second > 5  # Conservative threshold
    
    @pytest.mark.asyncio
    async def test_index_query_performance(self, perf_db):
        """Test index-based query performance"""
        # Mock index query results
        index_results = [{"id": i, "name": f"user{i}", "department": "Engineering"} 
                        for i in range(100)]
        
        with patch.object(perf_db, '_execute_lua_script') as mock_exec:
            import json
            mock_exec.return_value = json.dumps(index_results)
            
            iterations = 100
            start_time = time.time()
            
            for _ in range(iterations):
                results = await perf_db.select_by_index("users", "department", "Engineering")
                assert len(results) == 100
            
            end_time = time.time()
            duration = end_time - start_time
            queries_per_second = iterations / duration
            
            print(f"Index query performance: {queries_per_second:.0f} queries/sec")
            assert queries_per_second > 10  # Should be faster than full table scans


@pytest.mark.performance
class TestConcurrencyPerformance:
    """Test concurrent operation performance"""
    
    @pytest.fixture
    async def concurrent_db(self, temp_db_dir):
        """Create a database instance for concurrency testing"""
        with patch.object(ExtensibleDB, '_find_binary') as mock_find:
            mock_find.return_value = "/fake/binary"
            with patch.object(ExtensibleDB, '_validate_setup'):
                with patch.object(ExtensibleDB, '_execute_lua_script') as mock_exec:
                    mock_exec.return_value = "1"
                    db = ExtensibleDB(str(temp_db_dir / "concurrent.db"))
                    yield db
    
    @pytest.mark.asyncio
    async def test_concurrent_inserts(self, concurrent_db):
        """Test concurrent insert operations"""
        async def insert_worker(worker_id, num_inserts):
            """Worker function for concurrent inserts"""
            with patch.object(concurrent_db, '_execute_lua_script') as mock_exec:
                mock_exec.side_effect = [str(i) for i in range(1, num_inserts + 1)]
                
                for i in range(num_inserts):
                    await concurrent_db.insert_row("test_table", {
                        "worker_id": worker_id,
                        "item": i,
                        "data": f"test_data_{worker_id}_{i}"
                    })
                return num_inserts
        
        num_workers = 5
        inserts_per_worker = 20
        
        start_time = time.time()
        
        # Run concurrent workers
        tasks = [insert_worker(i, inserts_per_worker) for i in range(num_workers)]
        results = await asyncio.gather(*tasks)
        
        end_time = time.time()
        duration = end_time - start_time
        total_inserts = sum(results)
        ops_per_second = total_inserts / duration
        
        print(f"Concurrent inserts ({num_workers} workers): {ops_per_second:.0f} ops/sec")
        
        assert total_inserts == num_workers * inserts_per_worker
        assert ops_per_second > 20  # Should handle concurrent load
    
    @pytest.mark.asyncio
    async def test_mixed_workload_performance(self, concurrent_db):
        """Test mixed read/write workload performance"""
        async def write_worker(num_writes):
            """Worker for write operations"""
            with patch.object(concurrent_db, '_execute_lua_script') as mock_exec:
                mock_exec.side_effect = [str(i) for i in range(1, num_writes + 1)]
                
                for i in range(num_writes):
                    await concurrent_db.insert_row("test_table", {"write_id": i})
                return num_writes
        
        async def read_worker(num_reads):
            """Worker for read operations"""
            with patch.object(concurrent_db, '_execute_lua_script') as mock_exec:
                import json
                mock_data = [{"id": i, "data": f"item_{i}"} for i in range(10)]
                mock_exec.return_value = json.dumps(mock_data)
                
                for i in range(num_reads):
                    await concurrent_db.select_all("test_table")
                return num_reads
        
        num_writes = 30
        num_reads = 70
        
        start_time = time.time()
        
        # Run mixed workload
        write_task = write_worker(num_writes)
        read_task = read_worker(num_reads)
        
        write_result, read_result = await asyncio.gather(write_task, read_task)
        
        end_time = time.time()
        duration = end_time - start_time
        total_ops = write_result + read_result
        ops_per_second = total_ops / duration
        
        print(f"Mixed workload (70% reads, 30% writes): {ops_per_second:.0f} ops/sec")
        
        assert write_result == num_writes
        assert read_result == num_reads
        assert ops_per_second > 30


@pytest.mark.performance
class TestMemoryPerformance:
    """Test memory usage and efficiency"""
    
    @pytest.mark.asyncio
    async def test_large_dataset_handling(self, temp_db_dir):
        """Test handling of large datasets"""
        with patch.object(ExtensibleDB, '_find_binary') as mock_find:
            mock_find.return_value = "/fake/binary"
            with patch.object(ExtensibleDB, '_validate_setup'):
                db = ExtensibleDB(str(temp_db_dir / "large.db"))
                
                # Test with large dataset
                large_dataset = generate_test_users(10000)
                
                with patch.object(db, '_execute_lua_script') as mock_exec:
                    import json
                    
                    # Test serialization performance
                    start_time = time.time()
                    json_data = json.dumps(large_dataset)
                    serialization_time = time.time() - start_time
                    
                    print(f"JSON serialization of 10k records: {serialization_time:.3f}s")
                    
                    # Mock bulk insert
                    ids = '\n'.join(str(i) for i in range(1, len(large_dataset) + 1))
                    mock_exec.return_value = ids
                    
                    start_time = time.time()
                    result_ids = await db.insert_rows("large_table", large_dataset)
                    bulk_time = time.time() - start_time
                    
                    print(f"Bulk insert of 10k records: {bulk_time:.3f}s")
                    
                    assert len(result_ids) == len(large_dataset)
                    assert serialization_time < 1.0  # Should be fast
                    assert bulk_time < 2.0  # Should handle large batches efficiently
    
    @pytest.mark.asyncio
    async def test_response_time_consistency(self, temp_db_dir):
        """Test response time consistency under load"""
        with patch.object(ExtensibleDB, '_find_binary') as mock_find:
            mock_find.return_value = "/fake/binary"
            with patch.object(ExtensibleDB, '_validate_setup'):
                db = ExtensibleDB(str(temp_db_dir / "consistency.db"))
                
                response_times = []
                iterations = 50
                
                with patch.object(db, '_execute_lua_script') as mock_exec:
                    mock_exec.return_value = "1"
                    
                    for i in range(iterations):
                        start_time = time.time()
                        await db.insert_row("test_table", {"iteration": i})
                        response_time = time.time() - start_time
                        response_times.append(response_time)
                
                # Calculate statistics
                avg_time = statistics.mean(response_times)
                median_time = statistics.median(response_times)
                std_dev = statistics.stdev(response_times)
                
                print(f"Response time stats - Avg: {avg_time:.4f}s, "
                      f"Median: {median_time:.4f}s, StdDev: {std_dev:.4f}s")
                
                # Response times should be consistent (low standard deviation)
                assert std_dev < avg_time * 0.5  # StdDev should be < 50% of average
                assert max(response_times) < avg_time * 3  # No outliers > 3x average


@pytest.mark.performance
@pytest.mark.slow
class TestStressTests:
    """Stress tests for extreme conditions"""
    
    @pytest.mark.asyncio
    async def test_high_volume_stress(self, temp_db_dir):
        """Test system under high volume stress"""
        with patch.object(ExtensibleDB, '_find_binary') as mock_find:
            mock_find.return_value = "/fake/binary"
            with patch.object(ExtensibleDB, '_validate_setup'):
                db = ExtensibleDB(str(temp_db_dir / "stress.db"))
                
                # Simulate high volume operations
                total_operations = 1000
                batch_size = 100
                
                with patch.object(db, '_execute_lua_script') as mock_exec:
                    # Mock responses for bulk operations
                    def mock_response(*args):
                        return '\n'.join(str(i) for i in range(1, batch_size + 1))
                    
                    mock_exec.side_effect = [mock_response() for _ in range(total_operations // batch_size)]
                    
                    start_time = time.time()
                    
                    for batch_num in range(total_operations // batch_size):
                        batch_data = generate_test_users(batch_size)
                        await db.insert_rows("stress_table", batch_data)
                    
                    end_time = time.time()
                    duration = end_time - start_time
                    ops_per_second = total_operations / duration
                    
                    print(f"High volume stress test: {ops_per_second:.0f} ops/sec")
                    
                    # Should maintain reasonable performance under stress
                    assert ops_per_second > 100
                    assert duration < 30  # Should complete within reasonable time
    
    @pytest.mark.asyncio
    async def test_timeout_handling(self, temp_db_dir):
        """Test timeout handling under slow conditions"""
        with patch.object(ExtensibleDB, '_find_binary') as mock_find:
            mock_find.return_value = "/fake/binary"
            with patch.object(ExtensibleDB, '_validate_setup'):
                db = ExtensibleDB(str(temp_db_dir / "timeout.db"))
                
                # Mock slow script execution
                async def slow_mock(*args, **kwargs):
                    await asyncio.sleep(2)  # Simulate slow operation
                    return "1"
                
                with patch.object(db, '_execute_lua_script', side_effect=slow_mock):
                    start_time = time.time()
                    
                    try:
                        # This should timeout
                        await db.insert_row("test_table", {"data": "test"})
                        pytest.fail("Expected timeout error")
                    except Exception as e:
                        duration = time.time() - start_time
                        print(f"Timeout handling test completed in {duration:.2f}s")
                        
                        # Should timeout within reasonable time
                        assert duration < 35  # Default timeout is 30s + some overhead
                        assert "timeout" in str(e).lower() or "failed" in str(e).lower()
