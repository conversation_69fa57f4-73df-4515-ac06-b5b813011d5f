"""
Pytest configuration and fixtures for ExtensibleDB tests.

This module provides common fixtures and configuration for all test modules.
"""

import pytest
import asyncio
import tempfile
import shutil
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi.testclient import TestClient
from httpx import AsyncClient
import json

from src.db_wrapper import ExtensibleDB, ExtensibleDBError
from src.main import app, get_db
from src.models import User, Product, Order, model_to_dict


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
async def temp_db_dir():
    """Create a temporary directory for test database"""
    temp_dir = Path(tempfile.mkdtemp())
    yield temp_dir
    shutil.rmtree(temp_dir, ignore_errors=True)


@pytest.fixture
async def mock_db():
    """Create a mock ExtensibleDB instance for unit tests"""
    mock = AsyncMock(spec=ExtensibleDB)
    
    # Configure default return values
    mock.create_table.return_value = True
    mock.create_index.return_value = True
    mock.insert_row.return_value = 1
    mock.insert_rows.return_value = [1, 2, 3]
    mock.select_all.return_value = []
    mock.select_by_index.return_value = []
    mock.begin_transaction.return_value = True
    mock.commit_transaction.return_value = True
    mock.rollback_transaction.return_value = True
    mock.health_check.return_value = True
    
    return mock


@pytest.fixture
async def real_db(temp_db_dir):
    """Create a real ExtensibleDB instance for integration tests"""
    # Mock the binary path to avoid requiring actual ExtensibleDB binary
    with patch.object(ExtensibleDB, '_find_binary') as mock_find:
        mock_find.return_value = Path("/fake/binary")
        
        with patch.object(ExtensibleDB, '_validate_setup'):
            with patch.object(ExtensibleDB, '_execute_lua_script') as mock_exec:
                # Configure mock script execution
                mock_exec.return_value = "1"  # Default return for insert operations
                
                db = ExtensibleDB(str(temp_db_dir / "test.db"))
                yield db


@pytest.fixture
def test_client():
    """Create a test client for FastAPI app"""
    return TestClient(app)


@pytest.fixture
async def async_test_client():
    """Create an async test client for FastAPI app"""
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client


@pytest.fixture
def override_get_db(mock_db):
    """Override the get_db dependency with mock database"""
    def _override():
        return mock_db
    
    app.dependency_overrides[get_db] = _override
    yield mock_db
    app.dependency_overrides.clear()


@pytest.fixture
def sample_user_data():
    """Sample user data for testing"""
    return {
        "name": "John Doe",
        "email": "<EMAIL>",
        "age": 30,
        "department": "Engineering",
        "active": True
    }


@pytest.fixture
def sample_users_data():
    """Sample users data for bulk testing"""
    return [
        {
            "name": "Alice Johnson",
            "email": "<EMAIL>",
            "age": 28,
            "department": "Engineering"
        },
        {
            "name": "Bob Smith",
            "email": "<EMAIL>",
            "age": 32,
            "department": "Marketing"
        },
        {
            "name": "Carol Davis",
            "email": "<EMAIL>",
            "age": 29,
            "department": "Sales"
        }
    ]


@pytest.fixture
def sample_product_data():
    """Sample product data for testing"""
    return {
        "name": "Test Product",
        "price": 99.99,
        "category": "electronics",
        "stock": 10,
        "description": "A test product"
    }


@pytest.fixture
def sample_products_data():
    """Sample products data for bulk testing"""
    return [
        {
            "name": "Laptop",
            "price": 999.99,
            "category": "electronics",
            "stock": 5
        },
        {
            "name": "Mouse",
            "price": 29.99,
            "category": "electronics",
            "stock": 50
        },
        {
            "name": "Desk",
            "price": 299.99,
            "category": "furniture",
            "stock": 3
        }
    ]


@pytest.fixture
def sample_order_data():
    """Sample order data for testing"""
    return {
        "user_id": 1,
        "product_id": 1,
        "quantity": 2,
        "total_price": 199.98,
        "status": "pending"
    }


@pytest.fixture
def sample_user_model():
    """Sample User model instance"""
    return User(
        name="Test User",
        email="<EMAIL>",
        age=25,
        department="Testing"
    )


@pytest.fixture
def sample_product_model():
    """Sample Product model instance"""
    return Product(
        name="Test Product",
        price=49.99,
        category="test",
        stock=100
    )


@pytest.fixture
def sample_order_model():
    """Sample Order model instance"""
    return Order(
        user_id=1,
        product_id=1,
        quantity=1,
        total_price=49.99
    )


class MockSubprocess:
    """Mock subprocess for testing script execution"""
    
    def __init__(self, returncode=0, stdout="", stderr=""):
        self.returncode = returncode
        self.stdout = stdout.encode() if isinstance(stdout, str) else stdout
        self.stderr = stderr.encode() if isinstance(stderr, str) else stderr
    
    async def communicate(self):
        return self.stdout, self.stderr
    
    def kill(self):
        pass
    
    async def wait(self):
        return self.returncode


@pytest.fixture
def mock_subprocess_success():
    """Mock successful subprocess execution"""
    return MockSubprocess(returncode=0, stdout="success")


@pytest.fixture
def mock_subprocess_failure():
    """Mock failed subprocess execution"""
    return MockSubprocess(returncode=1, stderr="error")


@pytest.fixture
def mock_lua_script_responses():
    """Mock responses for different Lua script types"""
    return {
        'create_table': "Table 'test' created successfully",
        'create_index': "Index created on test.field",
        'insert_row': "1",
        'insert_rows': "1\n2\n3",
        'select_all': json.dumps([{"id": 1, "name": "test"}]),
        'select_by_index': json.dumps([{"id": 1, "name": "test"}]),
        'health_check': "health_ok"
    }


# Test data generators
def generate_test_users(count: int):
    """Generate test user data"""
    users = []
    for i in range(count):
        users.append({
            "name": f"User {i}",
            "email": f"user{i}@example.com",
            "age": 20 + (i % 50),
            "department": ["Engineering", "Marketing", "Sales"][i % 3]
        })
    return users


def generate_test_products(count: int):
    """Generate test product data"""
    products = []
    categories = ["electronics", "furniture", "clothing", "books"]
    for i in range(count):
        products.append({
            "name": f"Product {i}",
            "price": 10.0 + (i * 5.0),
            "category": categories[i % len(categories)],
            "stock": 10 + (i % 100)
        })
    return products


# Pytest configuration
def pytest_configure(config):
    """Configure pytest with custom markers"""
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", "performance: mark test as a performance test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )


# Async test utilities
async def wait_for_condition(condition_func, timeout=5.0, interval=0.1):
    """Wait for a condition to become true"""
    import time
    start_time = time.time()
    while time.time() - start_time < timeout:
        if await condition_func():
            return True
        await asyncio.sleep(interval)
    return False
