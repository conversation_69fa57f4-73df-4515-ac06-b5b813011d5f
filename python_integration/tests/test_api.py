"""
Integration tests for FastAPI endpoints.

These tests verify the FastAPI application endpoints work correctly
with mocked database operations.
"""

import pytest
from fastapi import status
from unittest.mock import AsyncMock

from src.db_wrapper import ExtensibleDBError


@pytest.mark.integration
class TestHealthEndpoints:
    """Test health check endpoints"""
    
    def test_health_check_success(self, test_client, override_get_db):
        """Test successful health check"""
        override_get_db.health_check.return_value = True
        
        response = test_client.get("/health")
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert data["success"] is True
        assert data["status"] == "healthy"
        assert data["database_responsive"] is True
    
    def test_health_check_degraded(self, test_client, override_get_db):
        """Test health check with degraded database"""
        override_get_db.health_check.return_value = False
        
        response = test_client.get("/health")
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert data["success"] is True
        assert data["status"] == "degraded"
        assert data["database_responsive"] is False


@pytest.mark.integration
class TestTableEndpoints:
    """Test table management endpoints"""
    
    def test_create_table_success(self, test_client, override_get_db):
        """Test successful table creation"""
        override_get_db.create_table.return_value = True
        
        response = test_client.post("/tables", json={"name": "test_table"})
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert data["success"] is True
        assert "test_table" in data["message"]
        
        override_get_db.create_table.assert_called_once_with("test_table")
    
    def test_create_table_invalid_name(self, test_client, override_get_db):
        """Test table creation with invalid name"""
        response = test_client.post("/tables", json={"name": "123invalid"})
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    def test_create_table_database_error(self, test_client, override_get_db):
        """Test table creation with database error"""
        override_get_db.create_table.side_effect = ExtensibleDBError("Database error")
        
        response = test_client.post("/tables", json={"name": "test_table"})
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        
        data = response.json()
        assert data["success"] is False
        assert "Database error" in data["error"]
    
    def test_create_index_success(self, test_client, override_get_db):
        """Test successful index creation"""
        override_get_db.create_index.return_value = True
        
        response = test_client.post(
            "/tables/test_table/indexes",
            json={"table_name": "test_table", "field_name": "test_field"}
        )
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert data["success"] is True
        assert "test_table.test_field" in data["message"]
        
        override_get_db.create_index.assert_called_once_with("test_table", "test_field")
    
    def test_create_index_table_name_mismatch(self, test_client, override_get_db):
        """Test index creation with table name mismatch"""
        response = test_client.post(
            "/tables/test_table/indexes",
            json={"table_name": "different_table", "field_name": "test_field"}
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST


@pytest.mark.integration
class TestDocumentEndpoints:
    """Test document management endpoints"""
    
    def test_insert_document_success(self, test_client, override_get_db, sample_user_data):
        """Test successful document insertion"""
        override_get_db.insert_row.return_value = 123
        
        response = test_client.post(
            "/tables/users/documents",
            json={"table_name": "users", "data": sample_user_data}
        )
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert data["success"] is True
        assert data["id"] == 123
        
        override_get_db.insert_row.assert_called_once_with("users", sample_user_data)
    
    def test_insert_document_table_name_mismatch(self, test_client, override_get_db, sample_user_data):
        """Test document insertion with table name mismatch"""
        response = test_client.post(
            "/tables/users/documents",
            json={"table_name": "different_table", "data": sample_user_data}
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST
    
    def test_bulk_insert_documents_success(self, test_client, override_get_db, sample_users_data):
        """Test successful bulk document insertion"""
        override_get_db.insert_rows.return_value = [1, 2, 3]
        
        response = test_client.post(
            "/tables/users/documents/bulk",
            json={"table_name": "users", "data": sample_users_data}
        )
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert data["success"] is True
        assert data["ids"] == [1, 2, 3]
        assert data["count"] == 3
        
        override_get_db.insert_rows.assert_called_once_with("users", sample_users_data)
    
    def test_bulk_insert_empty_data(self, test_client, override_get_db):
        """Test bulk insertion with empty data"""
        response = test_client.post(
            "/tables/users/documents/bulk",
            json={"table_name": "users", "data": []}
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


@pytest.mark.integration
class TestQueryEndpoints:
    """Test query endpoints"""
    
    def test_get_all_documents_success(self, test_client, override_get_db, sample_users_data):
        """Test successful get all documents"""
        override_get_db.select_all.return_value = sample_users_data
        
        response = test_client.get("/tables/users/documents")
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert data["success"] is True
        assert data["data"] == sample_users_data
        assert data["count"] == len(sample_users_data)
        assert data["total"] == len(sample_users_data)
        
        override_get_db.select_all.assert_called_once_with("users")
    
    def test_get_all_documents_with_pagination(self, test_client, override_get_db, sample_users_data):
        """Test get all documents with pagination"""
        override_get_db.select_all.return_value = sample_users_data
        
        response = test_client.get("/tables/users/documents?limit=2&offset=1")
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert data["success"] is True
        assert data["count"] == 2  # Limited to 2
        assert data["total"] == len(sample_users_data)  # Total is original count
        assert len(data["data"]) == 2
    
    def test_query_by_index_success(self, test_client, override_get_db):
        """Test successful index-based query"""
        expected_results = [{"id": 1, "name": "Alice", "department": "Engineering"}]
        override_get_db.select_by_index.return_value = expected_results
        
        response = test_client.post(
            "/tables/users/query",
            json={
                "table_name": "users",
                "field_name": "department",
                "value": "Engineering"
            }
        )
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert data["success"] is True
        assert data["data"] == expected_results
        assert data["count"] == 1
        
        override_get_db.select_by_index.assert_called_once_with("users", "department", "Engineering")
    
    def test_query_by_index_different_value_types(self, test_client, override_get_db):
        """Test index query with different value types"""
        override_get_db.select_by_index.return_value = []
        
        # String value
        response = test_client.post(
            "/tables/users/query",
            json={"table_name": "users", "field_name": "name", "value": "Alice"}
        )
        assert response.status_code == status.HTTP_200_OK
        
        # Integer value
        response = test_client.post(
            "/tables/users/query",
            json={"table_name": "users", "field_name": "age", "value": 30}
        )
        assert response.status_code == status.HTTP_200_OK
        
        # Boolean value
        response = test_client.post(
            "/tables/users/query",
            json={"table_name": "users", "field_name": "active", "value": True}
        )
        assert response.status_code == status.HTTP_200_OK


@pytest.mark.integration
class TestTransactionEndpoints:
    """Test transaction endpoints"""
    
    def test_begin_transaction_success(self, test_client, override_get_db):
        """Test successful transaction begin"""
        override_get_db.begin_transaction.return_value = True
        
        response = test_client.post("/transactions/begin")
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert data["success"] is True
        assert data["operation"] == "begin"
        
        override_get_db.begin_transaction.assert_called_once()
    
    def test_commit_transaction_success(self, test_client, override_get_db):
        """Test successful transaction commit"""
        override_get_db.commit_transaction.return_value = True
        
        response = test_client.post("/transactions/commit")
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert data["success"] is True
        assert data["operation"] == "commit"
        
        override_get_db.commit_transaction.assert_called_once()
    
    def test_rollback_transaction_success(self, test_client, override_get_db):
        """Test successful transaction rollback"""
        override_get_db.rollback_transaction.return_value = True
        
        response = test_client.post("/transactions/rollback")
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert data["success"] is True
        assert data["operation"] == "rollback"
        
        override_get_db.rollback_transaction.assert_called_once()


@pytest.mark.integration
class TestDomainEndpoints:
    """Test domain-specific endpoints"""
    
    def test_create_user_success(self, test_client, override_get_db):
        """Test successful user creation"""
        override_get_db.create_table.return_value = True
        override_get_db.create_index.return_value = True
        override_get_db.insert_row.return_value = 123
        
        user_data = {
            "name": "John Doe",
            "email": "<EMAIL>",
            "age": 30,
            "department": "Engineering"
        }
        
        response = test_client.post("/users", json=user_data)
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert data["success"] is True
        assert data["id"] == 123
        
        # Should attempt to create table and indexes
        override_get_db.create_table.assert_called_with("users")
        override_get_db.create_index.assert_any_call("users", "email")
        override_get_db.create_index.assert_any_call("users", "department")
    
    def test_get_users_success(self, test_client, override_get_db, sample_users_data):
        """Test successful get users"""
        override_get_db.select_all.return_value = sample_users_data
        
        response = test_client.get("/users")
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert data["success"] is True
        assert data["data"] == sample_users_data
        assert data["count"] == len(sample_users_data)
    
    def test_get_users_by_department(self, test_client, override_get_db):
        """Test get users by department"""
        expected_users = [{"id": 1, "name": "Alice", "department": "Engineering"}]
        override_get_db.select_by_index.return_value = expected_users
        
        response = test_client.get("/users/by-department/Engineering")
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert data["success"] is True
        assert data["data"] == expected_users
        
        override_get_db.select_by_index.assert_called_once_with("users", "department", "Engineering")
    
    def test_get_user_by_email(self, test_client, override_get_db):
        """Test get user by email"""
        expected_user = [{"id": 1, "name": "Alice", "email": "<EMAIL>"}]
        override_get_db.select_by_index.return_value = expected_user
        
        response = test_client.get("/users/by-email/<EMAIL>")
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert data["success"] is True
        assert data["data"] == expected_user
        
        override_get_db.select_by_index.assert_called_once_with("users", "email", "<EMAIL>")
    
    def test_create_product_success(self, test_client, override_get_db):
        """Test successful product creation"""
        override_get_db.create_table.return_value = True
        override_get_db.create_index.return_value = True
        override_get_db.insert_row.return_value = 456
        
        product_data = {
            "name": "Test Product",
            "price": 99.99,
            "category": "electronics",
            "stock": 10
        }
        
        response = test_client.post("/products", json=product_data)
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert data["success"] is True
        assert data["id"] == 456
    
    def test_get_products_by_category(self, test_client, override_get_db):
        """Test get products by category"""
        expected_products = [{"id": 1, "name": "Laptop", "category": "electronics"}]
        override_get_db.select_by_index.return_value = expected_products
        
        response = test_client.get("/products/by-category/electronics")
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert data["success"] is True
        assert data["data"] == expected_products
        
        override_get_db.select_by_index.assert_called_once_with("products", "category", "electronics")


@pytest.mark.integration
class TestErrorHandling:
    """Test error handling in API endpoints"""
    
    def test_database_not_initialized(self, test_client):
        """Test behavior when database is not initialized"""
        # Don't override get_db, so it will return None
        response = test_client.get("/health")
        assert response.status_code == status.HTTP_503_SERVICE_UNAVAILABLE
    
    def test_validation_error_handling(self, test_client, override_get_db):
        """Test validation error handling"""
        # Invalid table name
        response = test_client.post("/tables", json={"name": "123invalid"})
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        
        data = response.json()
        assert data["success"] is False
        assert "error" in data
    
    def test_database_error_handling(self, test_client, override_get_db):
        """Test database error handling"""
        override_get_db.create_table.side_effect = ExtensibleDBError("Database connection failed")
        
        response = test_client.post("/tables", json={"name": "test_table"})
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        
        data = response.json()
        assert data["success"] is False
        assert "Database connection failed" in data["error"]
    
    def test_general_exception_handling(self, test_client, override_get_db):
        """Test general exception handling"""
        override_get_db.create_table.side_effect = Exception("Unexpected error")
        
        response = test_client.post("/tables", json={"name": "test_table"})
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        
        data = response.json()
        assert data["success"] is False
        assert "Internal server error" in data["error"]
