"""
Unit tests for ExtensibleDB wrapper.

These tests focus on testing the ExtensibleDB wrapper class in isolation,
using mocks to avoid dependencies on the actual Rust binary.
"""

import pytest
import asyncio
import json
from unittest.mock import AsyncMock, patch, MagicMock
from pathlib import Path

from src.db_wrapper import ExtensibleDB, ExtensibleDBError


@pytest.mark.unit
class TestExtensibleDBInit:
    """Test ExtensibleDB initialization"""
    
    def test_init_with_default_paths(self):
        """Test initialization with default paths"""
        with patch.object(ExtensibleDB, '_find_binary') as mock_find:
            mock_find.return_value = Path("/fake/binary")
            with patch.object(ExtensibleDB, '_validate_setup'):
                db = ExtensibleDB()
                assert db.db_path == Path("./data.db")
                assert db.binary_path == Path("/fake/binary")
    
    def test_init_with_custom_paths(self):
        """Test initialization with custom paths"""
        with patch.object(ExtensibleDB, '_find_binary') as mock_find:
            mock_find.return_value = Path("/custom/binary")
            with patch.object(ExtensibleDB, '_validate_setup'):
                db = ExtensibleDB("/custom/db", "/custom/binary")
                assert db.db_path == Path("/custom/db")
                assert db.binary_path == Path("/custom/binary")
    
    def test_find_binary_with_explicit_path(self):
        """Test binary finding with explicit path"""
        with patch.object(ExtensibleDB, '_validate_setup'):
            db = ExtensibleDB()
            result = db._find_binary("/explicit/path")
            assert result == Path("/explicit/path")
    
    def test_find_binary_auto_detection(self):
        """Test automatic binary detection"""
        with patch.object(ExtensibleDB, '_validate_setup'):
            with patch.object(Path, 'exists') as mock_exists:
                mock_exists.side_effect = lambda: str(self) == "./target/release/extensibledb"
                db = ExtensibleDB()
                result = db._find_binary(None)
                assert result == Path("./target/release/extensibledb")
    
    def test_find_binary_not_found(self):
        """Test binary not found error"""
        with patch.object(Path, 'exists', return_value=False):
            with pytest.raises(ExtensibleDBError, match="ExtensibleDB binary not found"):
                ExtensibleDB()
    
    def test_validate_setup_binary_not_exists(self):
        """Test validation when binary doesn't exist"""
        with patch.object(ExtensibleDB, '_find_binary') as mock_find:
            mock_find.return_value = Path("/nonexistent/binary")
            with patch.object(Path, 'exists', return_value=False):
                with pytest.raises(ExtensibleDBError, match="ExtensibleDB binary not found"):
                    ExtensibleDB()
    
    def test_validate_setup_binary_not_executable(self):
        """Test validation when binary is not executable"""
        with patch.object(ExtensibleDB, '_find_binary') as mock_find:
            mock_find.return_value = Path("/fake/binary")
            with patch.object(Path, 'exists', return_value=True):
                with patch('os.access', return_value=False):
                    with pytest.raises(ExtensibleDBError, match="ExtensibleDB binary is not executable"):
                        ExtensibleDB()


@pytest.mark.unit
class TestExtensibleDBScriptExecution:
    """Test Lua script execution"""
    
    @pytest.fixture
    def db_instance(self):
        """Create a test database instance"""
        with patch.object(ExtensibleDB, '_find_binary') as mock_find:
            mock_find.return_value = Path("/fake/binary")
            with patch.object(ExtensibleDB, '_validate_setup'):
                return ExtensibleDB()
    
    @pytest.mark.asyncio
    async def test_execute_lua_script_success(self, db_instance, mock_subprocess_success):
        """Test successful script execution"""
        with patch('asyncio.create_subprocess_exec') as mock_create:
            mock_create.return_value = mock_subprocess_success
            
            result = await db_instance._execute_lua_script("print('test')")
            assert result == "success"
    
    @pytest.mark.asyncio
    async def test_execute_lua_script_failure(self, db_instance, mock_subprocess_failure):
        """Test failed script execution"""
        with patch('asyncio.create_subprocess_exec') as mock_create:
            mock_create.return_value = mock_subprocess_failure
            
            with pytest.raises(ExtensibleDBError, match="Script execution failed"):
                await db_instance._execute_lua_script("invalid script")
    
    @pytest.mark.asyncio
    async def test_execute_lua_script_timeout(self, db_instance):
        """Test script execution timeout"""
        mock_process = AsyncMock()
        mock_process.communicate.side_effect = asyncio.TimeoutError()
        mock_process.kill = MagicMock()
        mock_process.wait = AsyncMock()
        
        with patch('asyncio.create_subprocess_exec', return_value=mock_process):
            with pytest.raises(ExtensibleDBError, match="Script execution timed out"):
                await db_instance._execute_lua_script("slow script", timeout=0.1)
    
    @pytest.mark.asyncio
    async def test_execute_lua_script_cleanup(self, db_instance, mock_subprocess_success):
        """Test that temporary files are cleaned up"""
        with patch('asyncio.create_subprocess_exec') as mock_create:
            mock_create.return_value = mock_subprocess_success
            with patch('os.unlink') as mock_unlink:
                await db_instance._execute_lua_script("print('test')")
                mock_unlink.assert_called_once()


@pytest.mark.unit
class TestExtensibleDBOperations:
    """Test database operations"""
    
    @pytest.fixture
    def db_instance(self):
        """Create a test database instance"""
        with patch.object(ExtensibleDB, '_find_binary') as mock_find:
            mock_find.return_value = Path("/fake/binary")
            with patch.object(ExtensibleDB, '_validate_setup'):
                return ExtensibleDB()
    
    @pytest.mark.asyncio
    async def test_create_table_success(self, db_instance):
        """Test successful table creation"""
        with patch.object(db_instance, '_execute_lua_script') as mock_exec:
            mock_exec.return_value = "Table 'test' created successfully"
            
            result = await db_instance.create_table("test_table")
            assert result is True
            mock_exec.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_create_table_invalid_name(self, db_instance):
        """Test table creation with invalid name"""
        with pytest.raises(ExtensibleDBError, match="Invalid table name"):
            await db_instance.create_table("")
        
        with pytest.raises(ExtensibleDBError, match="Invalid table name"):
            await db_instance.create_table("123invalid")
    
    @pytest.mark.asyncio
    async def test_create_index_success(self, db_instance):
        """Test successful index creation"""
        with patch.object(db_instance, '_execute_lua_script') as mock_exec:
            mock_exec.return_value = "Index created"
            
            result = await db_instance.create_index("test_table", "test_field")
            assert result is True
            mock_exec.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_create_index_invalid_params(self, db_instance):
        """Test index creation with invalid parameters"""
        with pytest.raises(ExtensibleDBError, match="Table name and field name are required"):
            await db_instance.create_index("", "field")
        
        with pytest.raises(ExtensibleDBError, match="Table name and field name are required"):
            await db_instance.create_index("table", "")
    
    @pytest.mark.asyncio
    async def test_insert_row_success(self, db_instance):
        """Test successful row insertion"""
        with patch.object(db_instance, '_execute_lua_script') as mock_exec:
            mock_exec.return_value = "123"
            
            data = {"name": "test", "value": 42}
            result = await db_instance.insert_row("test_table", data)
            assert result == 123
            mock_exec.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_insert_row_invalid_params(self, db_instance):
        """Test row insertion with invalid parameters"""
        with pytest.raises(ExtensibleDBError, match="Table name and data are required"):
            await db_instance.insert_row("", {"data": "test"})
        
        with pytest.raises(ExtensibleDBError, match="Table name and data are required"):
            await db_instance.insert_row("table", {})
    
    @pytest.mark.asyncio
    async def test_insert_row_invalid_response(self, db_instance):
        """Test row insertion with invalid response"""
        with patch.object(db_instance, '_execute_lua_script') as mock_exec:
            mock_exec.return_value = "invalid_id"
            
            with pytest.raises(ExtensibleDBError, match="Failed to parse inserted row ID"):
                await db_instance.insert_row("test_table", {"data": "test"})
    
    @pytest.mark.asyncio
    async def test_insert_rows_success(self, db_instance):
        """Test successful bulk row insertion"""
        with patch.object(db_instance, '_execute_lua_script') as mock_exec:
            mock_exec.return_value = "1\n2\n3"
            
            data = [{"name": "test1"}, {"name": "test2"}, {"name": "test3"}]
            result = await db_instance.insert_rows("test_table", data)
            assert result == [1, 2, 3]
            mock_exec.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_insert_rows_too_many(self, db_instance):
        """Test bulk insertion with too many records"""
        data = [{"name": f"test{i}"} for i in range(10001)]
        
        with pytest.raises(ExtensibleDBError, match="Bulk insert limited to 10,000 records"):
            await db_instance.insert_rows("test_table", data)
    
    @pytest.mark.asyncio
    async def test_select_all_success(self, db_instance):
        """Test successful select all operation"""
        expected_data = [{"id": 1, "name": "test1"}, {"id": 2, "name": "test2"}]
        with patch.object(db_instance, '_execute_lua_script') as mock_exec:
            mock_exec.return_value = json.dumps(expected_data)
            
            result = await db_instance.select_all("test_table")
            assert result == expected_data
            mock_exec.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_select_all_invalid_json(self, db_instance):
        """Test select all with invalid JSON response"""
        with patch.object(db_instance, '_execute_lua_script') as mock_exec:
            mock_exec.return_value = "invalid json"
            
            with pytest.raises(ExtensibleDBError, match="Failed to parse JSON output"):
                await db_instance.select_all("test_table")
    
    @pytest.mark.asyncio
    async def test_select_by_index_success(self, db_instance):
        """Test successful index-based selection"""
        expected_data = [{"id": 1, "name": "test", "category": "A"}]
        with patch.object(db_instance, '_execute_lua_script') as mock_exec:
            mock_exec.return_value = json.dumps(expected_data)
            
            result = await db_instance.select_by_index("test_table", "category", "A")
            assert result == expected_data
            mock_exec.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_select_by_index_invalid_params(self, db_instance):
        """Test index selection with invalid parameters"""
        with pytest.raises(ExtensibleDBError, match="Table name and field name are required"):
            await db_instance.select_by_index("", "field", "value")
    
    @pytest.mark.asyncio
    async def test_health_check_success(self, db_instance):
        """Test successful health check"""
        with patch.object(db_instance, '_execute_lua_script') as mock_exec:
            mock_exec.return_value = "health_ok"
            
            result = await db_instance.health_check()
            assert result is True
    
    @pytest.mark.asyncio
    async def test_health_check_failure(self, db_instance):
        """Test failed health check"""
        with patch.object(db_instance, '_execute_lua_script') as mock_exec:
            mock_exec.side_effect = ExtensibleDBError("Connection failed")
            
            result = await db_instance.health_check()
            assert result is False


@pytest.mark.unit
class TestExtensibleDBTransactions:
    """Test transaction operations"""
    
    @pytest.fixture
    def db_instance(self):
        """Create a test database instance"""
        with patch.object(ExtensibleDB, '_find_binary') as mock_find:
            mock_find.return_value = Path("/fake/binary")
            with patch.object(ExtensibleDB, '_validate_setup'):
                return ExtensibleDB()
    
    @pytest.mark.asyncio
    async def test_begin_transaction(self, db_instance):
        """Test begin transaction"""
        with patch.object(db_instance, '_execute_lua_script') as mock_exec:
            result = await db_instance.begin_transaction()
            assert result is True
            mock_exec.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_commit_transaction(self, db_instance):
        """Test commit transaction"""
        with patch.object(db_instance, '_execute_lua_script') as mock_exec:
            result = await db_instance.commit_transaction()
            assert result is True
            mock_exec.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_rollback_transaction(self, db_instance):
        """Test rollback transaction"""
        with patch.object(db_instance, '_execute_lua_script') as mock_exec:
            result = await db_instance.rollback_transaction()
            assert result is True
            mock_exec.assert_called_once()
