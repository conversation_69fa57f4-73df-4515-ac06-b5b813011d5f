[tool:pytest]
# Pytest configuration for ExtensibleDB Python integration tests

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Markers
markers =
    unit: Unit tests (fast, isolated)
    integration: Integration tests (slower, with dependencies)
    performance: Performance and load tests
    slow: Slow running tests (may take several seconds)

# Output options
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes

# Minimum version
minversion = 6.0

# Test timeout (in seconds)
timeout = 300

# Asyncio configuration
asyncio_mode = auto

# Coverage options (if pytest-cov is installed)
# addopts = --cov=src --cov-report=html --cov-report=term-missing

# Filtering
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
