# Development dependencies for ExtensibleDB Python integration

# Include production requirements
-r requirements.txt

# Testing framework and plugins
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
pytest-timeout==2.2.0
pytest-xdist==3.5.0  # For parallel test execution

# Code quality and formatting
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
bandit==1.7.5  # Security linting

# Documentation
sphinx==7.2.6
sphinx-rtd-theme==1.3.0

# Development utilities
ipython==8.17.2
jupyter==1.0.0
pre-commit==3.6.0

# Performance profiling
memory-profiler==0.61.0
line-profiler==4.1.1

# Type checking stubs
types-requests==*********
