# ExtensibleDB Python Integration

A comprehensive Python integration for ExtensibleDB, featuring FastAPI REST API, async operations, and comprehensive testing.

## 🚀 Quick Start

```bash
# 1. Install dependencies
pip install -r requirements.txt

# 2. Run basic examples
python examples/basic_usage.py

# 3. Start the FastAPI server
python scripts/start_server.py

# 4. Visit API documentation
# http://localhost:8000/docs
```

## 📁 Project Structure

```
python_integration/
├── src/                          # Source code
│   ├── __init__.py
│   ├── db_wrapper.py            # ExtensibleDB Python wrapper
│   ├── models.py                # Pydantic models
│   └── main.py                  # FastAPI application
├── tests/                       # Comprehensive test suite
│   ├── __init__.py
│   ├── conftest.py             # Pytest configuration
│   ├── test_db_wrapper.py      # Database wrapper tests
│   ├── test_models.py          # Model validation tests
│   ├── test_api.py             # API endpoint tests
│   ├── test_performance.py     # Performance tests
│   └── test_integration.py     # End-to-end tests
├── examples/                    # Usage examples
│   ├── __init__.py
│   ├── basic_usage.py          # Basic operations
│   ├── advanced_usage.py       # Advanced features
│   └── performance_demo.py     # Performance examples
├── scripts/                     # Utility scripts
│   ├── start_server.py         # Server startup
│   └── setup_demo.py           # Automated setup
├── requirements.txt             # Production dependencies
├── requirements-dev.txt         # Development dependencies
├── pytest.ini                  # Test configuration
└── README.md                   # This file
```

## 🔧 Installation

### Prerequisites

1. **ExtensibleDB**: Ensure ExtensibleDB is built
   ```bash
   cd .. && cargo build --release
   ```

2. **Python 3.8+**: Required for async/await support

### Setup

```bash
# Clone and navigate to python integration
cd python_integration

# Install production dependencies
pip install -r requirements.txt

# Install development dependencies (optional)
pip install -r requirements-dev.txt

# Run setup script (automated)
python scripts/setup_demo.py
```

## 🏗️ Architecture

### Core Components

1. **`db_wrapper.py`**: Python wrapper for ExtensibleDB
   - Async subprocess communication
   - JSON serialization/deserialization
   - Error handling and validation
   - Connection management

2. **`models.py`**: Pydantic models for type safety
   - Request/response validation
   - Domain models (User, Product, Order)
   - Utility functions

3. **`main.py`**: FastAPI application
   - REST API endpoints
   - Dependency injection
   - Error handling middleware
   - Auto-generated documentation

### Integration Method

This implementation uses **subprocess integration**:
- Python executes Lua scripts via ExtensibleDB binary
- Data exchanged using JSON serialization
- Async operations prevent blocking
- Proper error handling and timeouts

## 📚 API Documentation

### Core Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/health` | Health check |
| `POST` | `/tables` | Create table |
| `POST` | `/tables/{table}/indexes` | Create index |
| `POST` | `/tables/{table}/documents` | Insert document |
| `POST` | `/tables/{table}/documents/bulk` | Bulk insert |
| `GET` | `/tables/{table}/documents` | Get all documents |
| `POST` | `/tables/{table}/query` | Query by index |

### Transaction Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/transactions/begin` | Begin transaction |
| `POST` | `/transactions/commit` | Commit transaction |
| `POST` | `/transactions/rollback` | Rollback transaction |

### Example Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/users` | Create user |
| `GET` | `/users` | Get all users |
| `GET` | `/users/by-department/{dept}` | Get users by department |
| `POST` | `/products` | Create product |
| `GET` | `/products/by-category/{cat}` | Get products by category |

## 🧪 Testing

### Test Categories

- **Unit Tests**: Fast, isolated component tests
- **Integration Tests**: Component interaction tests
- **Performance Tests**: Load and performance benchmarks
- **API Tests**: FastAPI endpoint tests

### Running Tests

```bash
# Run all tests
pytest

# Run specific test categories
pytest -m unit          # Unit tests only
pytest -m integration   # Integration tests only
pytest -m performance   # Performance tests only

# Run with coverage
pytest --cov=src --cov-report=html

# Run specific test file
pytest tests/test_db_wrapper.py -v
```

### Test Configuration

Tests use comprehensive mocking to avoid dependencies on the actual ExtensibleDB binary, making them fast and reliable.

## 📊 Performance

### Benchmarks

Based on ExtensibleDB's performance characteristics:

- **Single Inserts**: 12,000+ ops/sec (database) → ~100-1000 ops/sec (with subprocess overhead)
- **Bulk Inserts**: 16,000+ ops/sec (database) → ~1000-5000 ops/sec (with subprocess overhead)
- **Index Queries**: 45,000+ ops/sec (database) → ~1000-10000 ops/sec (with subprocess overhead)

### Performance Considerations

- Subprocess overhead adds 1-5ms latency per operation
- Bulk operations are more efficient than single operations
- JSON serialization/deserialization adds minimal overhead
- Async operations prevent blocking

## 🔍 Usage Examples

### Basic Operations

```python
from src.db_wrapper import ExtensibleDB

# Initialize database
db = ExtensibleDB()

# Create table and index
await db.create_table("users")
await db.create_index("users", "email")

# Insert data
user_id = await db.insert_row("users", {
    "name": "Alice",
    "email": "<EMAIL>",
    "age": 30
})

# Query data
users = await db.select_by_index("users", "email", "<EMAIL>")
```

### Using Pydantic Models

```python
from src.models import User, model_to_dict

# Create validated user
user = User(
    name="Bob Smith",
    email="<EMAIL>",
    age=25,
    department="Engineering"
)

# Convert to dict for database storage
user_data = model_to_dict(user)
user_id = await db.insert_row("users", user_data)
```

### FastAPI Integration

```python
from fastapi import FastAPI, Depends
from src.db_wrapper import ExtensibleDB
from src.models import User, InsertResponse

app = FastAPI()

@app.post("/users", response_model=InsertResponse)
async def create_user(user: User, db: ExtensibleDB = Depends(get_db)):
    user_data = model_to_dict(user)
    user_id = await db.insert_row("users", user_data)
    return InsertResponse(id=user_id)
```

## 🚀 Development

### Code Quality

```bash
# Format code
black src/ tests/ examples/

# Sort imports
isort src/ tests/ examples/

# Lint code
flake8 src/ tests/ examples/

# Type checking
mypy src/
```

### Adding New Features

1. **Database Operations**: Add methods to `db_wrapper.py`
2. **API Endpoints**: Add routes to `main.py`
3. **Models**: Add Pydantic models to `models.py`
4. **Tests**: Add comprehensive tests for new features

## 🔮 Future Enhancements

### Planned Improvements

- [ ] **FFI Integration**: Direct Rust-Python bindings for better performance
- [ ] **Connection Pooling**: Proper connection management
- [ ] **Streaming Results**: Handle large result sets efficiently
- [ ] **Advanced Queries**: Range queries, complex filters
- [ ] **Schema Validation**: Optional JSON schema enforcement

### Production Considerations

- [ ] **Authentication**: Add API authentication
- [ ] **Rate Limiting**: Implement request rate limiting
- [ ] **Monitoring**: Add metrics and logging
- [ ] **Caching**: Query result caching
- [ ] **Horizontal Scaling**: Multi-instance deployment

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add comprehensive tests
4. Ensure all tests pass
5. Follow code quality standards
6. Submit a pull request

## 📄 License

This project follows the same license as ExtensibleDB (MIT OR Apache-2.0).

---

**Ready to build high-performance applications with ExtensibleDB and Python!** 🚀
