"""
Pydantic models for ExtensibleDB FastAPI integration.

These models provide type safety and validation for API requests and responses.
"""

from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
import json
import re


class TableCreateRequest(BaseModel):
    """Request model for creating a table"""
    name: str = Field(..., min_length=1, max_length=100, description="Table name")
    
    @validator('name')
    def validate_table_name(cls, v):
        if not re.match(r'^[a-zA-Z][a-zA-Z0-9_]*$', v):
            raise ValueError('Table name must start with a letter and contain only alphanumeric characters and underscores')
        return v


class IndexCreateRequest(BaseModel):
    """Request model for creating an index"""
    table_name: str = Field(..., min_length=1, max_length=100, description="Table name")
    field_name: str = Field(..., min_length=1, max_length=100, description="Field name to index")
    
    @validator('table_name', 'field_name')
    def validate_names(cls, v):
        if not re.match(r'^[a-zA-Z][a-zA-Z0-9_]*$', v):
            raise ValueError('Names must start with a letter and contain only alphanumeric characters and underscores')
        return v


class DocumentInsertRequest(BaseModel):
    """Request model for inserting a single document"""
    table_name: str = Field(..., min_length=1, max_length=100, description="Table name")
    data: Dict[str, Any] = Field(..., description="Document data as JSON object")
    
    @validator('table_name')
    def validate_table_name(cls, v):
        if not re.match(r'^[a-zA-Z][a-zA-Z0-9_]*$', v):
            raise ValueError('Table name must start with a letter and contain only alphanumeric characters and underscores')
        return v
    
    @validator('data')
    def validate_data(cls, v):
        if not v:
            raise ValueError('Data cannot be empty')
        # Ensure data is JSON serializable
        try:
            json.dumps(v)
        except (TypeError, ValueError):
            raise ValueError('Data must be JSON serializable')
        return v


class BulkInsertRequest(BaseModel):
    """Request model for bulk document insertion"""
    table_name: str = Field(..., min_length=1, max_length=100, description="Table name")
    data: List[Dict[str, Any]] = Field(..., min_items=1, max_items=10000, description="List of documents")
    
    @validator('table_name')
    def validate_table_name(cls, v):
        if not re.match(r'^[a-zA-Z][a-zA-Z0-9_]*$', v):
            raise ValueError('Table name must start with a letter and contain only alphanumeric characters and underscores')
        return v
    
    @validator('data')
    def validate_data(cls, v):
        if not v:
            raise ValueError('Data list cannot be empty')
        for i, item in enumerate(v):
            if not item:
                raise ValueError(f'Data item at index {i} cannot be empty')
            try:
                json.dumps(item)
            except (TypeError, ValueError):
                raise ValueError(f'Data item at index {i} must be JSON serializable')
        return v


class QueryByIndexRequest(BaseModel):
    """Request model for querying by index"""
    table_name: str = Field(..., min_length=1, max_length=100, description="Table name")
    field_name: str = Field(..., min_length=1, max_length=100, description="Indexed field name")
    value: Union[str, int, float, bool] = Field(..., description="Value to search for")
    
    @validator('table_name', 'field_name')
    def validate_names(cls, v):
        if not re.match(r'^[a-zA-Z][a-zA-Z0-9_]*$', v):
            raise ValueError('Names must start with a letter and contain only alphanumeric characters and underscores')
        return v


class TableQueryRequest(BaseModel):
    """Request model for table queries"""
    table_name: str = Field(..., min_length=1, max_length=100, description="Table name")
    limit: Optional[int] = Field(None, ge=1, le=10000, description="Maximum number of results")
    offset: Optional[int] = Field(None, ge=0, description="Number of results to skip")
    
    @validator('table_name')
    def validate_table_name(cls, v):
        if not re.match(r'^[a-zA-Z][a-zA-Z0-9_]*$', v):
            raise ValueError('Table name must start with a letter and contain only alphanumeric characters and underscores')
        return v


# Response Models

class SuccessResponse(BaseModel):
    """Generic success response"""
    success: bool = True
    message: str
    timestamp: datetime = Field(default_factory=datetime.now)


class ErrorResponse(BaseModel):
    """Generic error response"""
    success: bool = False
    error: str
    details: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.now)


class InsertResponse(BaseModel):
    """Response for single document insertion"""
    success: bool = True
    id: int = Field(..., description="ID of the inserted document")
    message: str = "Document inserted successfully"
    timestamp: datetime = Field(default_factory=datetime.now)


class BulkInsertResponse(BaseModel):
    """Response for bulk document insertion"""
    success: bool = True
    ids: List[int] = Field(..., description="IDs of the inserted documents")
    count: int = Field(..., description="Number of documents inserted")
    message: str = "Documents inserted successfully"
    timestamp: datetime = Field(default_factory=datetime.now)
    
    @validator('count')
    def validate_count(cls, v, values):
        if 'ids' in values and len(values['ids']) != v:
            raise ValueError('Count must match the number of IDs')
        return v


class QueryResponse(BaseModel):
    """Response for document queries"""
    success: bool = True
    data: List[Dict[str, Any]] = Field(..., description="Query results")
    count: int = Field(..., description="Number of documents returned")
    total: Optional[int] = Field(None, description="Total number of documents (if known)")
    message: str = "Query executed successfully"
    timestamp: datetime = Field(default_factory=datetime.now)
    
    @validator('count')
    def validate_count(cls, v, values):
        if 'data' in values and len(values['data']) != v:
            raise ValueError('Count must match the number of data items')
        return v


class TransactionResponse(BaseModel):
    """Response for transaction operations"""
    success: bool = True
    operation: str = Field(..., description="Transaction operation (begin/commit/rollback)")
    message: str
    timestamp: datetime = Field(default_factory=datetime.now)


class HealthResponse(BaseModel):
    """Response for health check"""
    success: bool = True
    status: str = "healthy"
    database_responsive: bool = True
    timestamp: datetime = Field(default_factory=datetime.now)


# Example data models for common use cases

class User(BaseModel):
    """Example user model"""
    name: str = Field(..., min_length=1, max_length=100)
    email: str = Field(..., regex=r'^[^@]+@[^@]+\.[^@]+$')
    age: int = Field(..., ge=0, le=150)
    department: Optional[str] = Field(None, max_length=100)
    active: bool = Field(default=True)
    created_at: Optional[datetime] = Field(default_factory=datetime.now)
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
        schema_extra = {
            "example": {
                "name": "John Doe",
                "email": "<EMAIL>",
                "age": 30,
                "department": "Engineering",
                "active": True
            }
        }


class Product(BaseModel):
    """Example product model"""
    name: str = Field(..., min_length=1, max_length=200)
    price: float = Field(..., gt=0)
    category: str = Field(..., min_length=1, max_length=100)
    stock: int = Field(..., ge=0)
    description: Optional[str] = Field(None, max_length=1000)
    active: bool = Field(default=True)
    tags: Optional[List[str]] = Field(default_factory=list)
    created_at: Optional[datetime] = Field(default_factory=datetime.now)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
        schema_extra = {
            "example": {
                "name": "Laptop Pro",
                "price": 1299.99,
                "category": "electronics",
                "stock": 10,
                "description": "High-performance laptop",
                "tags": ["laptop", "computer", "electronics"]
            }
        }


class Order(BaseModel):
    """Example order model"""
    user_id: int = Field(..., gt=0)
    product_id: int = Field(..., gt=0)
    quantity: int = Field(..., gt=0)
    total_price: float = Field(..., gt=0)
    status: str = Field(default="pending", regex=r'^(pending|processing|shipped|delivered|cancelled)$')
    shipping_address: Optional[str] = Field(None, max_length=500)
    created_at: Optional[datetime] = Field(default_factory=datetime.now)
    updated_at: Optional[datetime] = Field(default_factory=datetime.now)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
        schema_extra = {
            "example": {
                "user_id": 1,
                "product_id": 1,
                "quantity": 2,
                "total_price": 2599.98,
                "status": "pending",
                "shipping_address": "123 Main St, City, State 12345"
            }
        }


# Utility functions for model conversion

def model_to_dict(model: BaseModel) -> Dict[str, Any]:
    """Convert a Pydantic model to a dictionary suitable for ExtensibleDB"""
    return json.loads(model.json())


def dict_to_model(data: Dict[str, Any], model_class: BaseModel) -> BaseModel:
    """Convert a dictionary from ExtensibleDB to a Pydantic model"""
    return model_class(**data)


def validate_json_serializable(data: Any) -> bool:
    """Check if data is JSON serializable"""
    try:
        json.dumps(data)
        return True
    except (TypeError, ValueError):
        return False
