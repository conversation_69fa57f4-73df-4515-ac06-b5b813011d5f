"""
FastAPI application for ExtensibleDB integration.

This application provides a REST API interface to ExtensibleDB,
demonstrating how to use the database from Python with FastAPI.
"""

from fastapi import FastAP<PERSON>, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.openapi.docs import get_swagger_ui_html
from fastapi.openapi.utils import get_openapi
import logging
from contextlib import asynccontextmanager
from typing import List, Dict, Any
import time

from .db_wrapper import ExtensibleDB, ExtensibleDBError
from .models import (
    TableCreateRequest, IndexCreateRequest, DocumentInsertRequest,
    BulkInsertRequest, QueryByIndexRequest, TableQueryRequest,
    SuccessResponse, ErrorResponse, InsertResponse, BulkInsertResponse,
    QueryResponse, TransactionResponse, HealthResponse,
    User, Product, Order, model_to_dict
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global database instance
db_instance = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global db_instance
    
    # Startup
    logger.info("Starting ExtensibleDB FastAPI application")
    try:
        db_instance = ExtensibleDB()
        
        # Perform health check
        if await db_instance.health_check():
            logger.info("ExtensibleDB wrapper initialized and health check passed")
        else:
            logger.warning("ExtensibleDB health check failed")
            
    except Exception as e:
        logger.error(f"Failed to initialize ExtensibleDB: {e}")
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down ExtensibleDB FastAPI application")


# Create FastAPI app
app = FastAPI(
    title="ExtensibleDB API",
    description="""
    REST API for ExtensibleDB - A high-performance embedded database engine.
    
    ## Features
    
    * **High Performance**: 16,000+ bulk inserts/sec, 45,000+ index queries/sec
    * **NoSQL Flexibility**: Schema-less JSON document storage
    * **Secondary Indexes**: Fast queries on any field
    * **Transactions**: ACID transaction support
    * **Embedded**: Zero-config, single-directory database
    
    ## Getting Started
    
    1. Create a table: `POST /tables`
    2. Create indexes: `POST /tables/{table}/indexes`
    3. Insert data: `POST /tables/{table}/documents`
    4. Query data: `GET /tables/{table}/documents` or `POST /tables/{table}/query`
    """,
    version="1.0.0",
    lifespan=lifespan,
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Middleware for request timing
@app.middleware("http")
async def add_process_time_header(request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response


def get_db() -> ExtensibleDB:
    """Dependency to get database instance"""
    if db_instance is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Database not initialized"
        )
    return db_instance


@app.exception_handler(ExtensibleDBError)
async def extensibledb_exception_handler(request, exc: ExtensibleDBError):
    """Handle ExtensibleDB specific errors"""
    logger.error(f"ExtensibleDB error: {exc}")
    return JSONResponse(
        status_code=status.HTTP_400_BAD_REQUEST,
        content=ErrorResponse(error=str(exc)).dict()
    )


@app.exception_handler(ValueError)
async def value_error_handler(request, exc: ValueError):
    """Handle validation errors"""
    logger.error(f"Validation error: {exc}")
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=ErrorResponse(error=str(exc)).dict()
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc: Exception):
    """Handle general exceptions"""
    logger.error(f"Unexpected error: {exc}", exc_info=True)
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=ErrorResponse(error="Internal server error").dict()
    )


# Health check endpoint
@app.get("/health", response_model=HealthResponse, tags=["Health"])
async def health_check(db: ExtensibleDB = Depends(get_db)):
    """
    Health check endpoint
    
    Returns the health status of the API and database connectivity.
    """
    db_responsive = await db.health_check()
    return HealthResponse(
        database_responsive=db_responsive,
        status="healthy" if db_responsive else "degraded"
    )


# Table management endpoints
@app.post("/tables", response_model=SuccessResponse, tags=["Tables"])
async def create_table(
    request: TableCreateRequest,
    db: ExtensibleDB = Depends(get_db)
):
    """
    Create a new table
    
    Creates a new table in the database. Table names must start with a letter
    and contain only alphanumeric characters and underscores.
    """
    await db.create_table(request.name)
    return SuccessResponse(message=f"Table '{request.name}' created successfully")


@app.post("/tables/{table_name}/indexes", response_model=SuccessResponse, tags=["Tables"])
async def create_index(
    table_name: str,
    request: IndexCreateRequest,
    db: ExtensibleDB = Depends(get_db)
):
    """
    Create an index on a table field
    
    Creates a secondary index on the specified field for fast queries.
    The table must exist before creating an index.
    """
    if request.table_name != table_name:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Table name in URL must match table name in request body"
        )
    
    await db.create_index(request.table_name, request.field_name)
    return SuccessResponse(
        message=f"Index created on {request.table_name}.{request.field_name}"
    )


# Document management endpoints
@app.post("/tables/{table_name}/documents", response_model=InsertResponse, tags=["Documents"])
async def insert_document(
    table_name: str,
    request: DocumentInsertRequest,
    db: ExtensibleDB = Depends(get_db)
):
    """
    Insert a single document into a table
    
    Inserts a JSON document into the specified table and returns the generated ID.
    """
    if request.table_name != table_name:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Table name in URL must match table name in request body"
        )
    
    doc_id = await db.insert_row(request.table_name, request.data)
    return InsertResponse(id=doc_id)


@app.post("/tables/{table_name}/documents/bulk", response_model=BulkInsertResponse, tags=["Documents"])
async def bulk_insert_documents(
    table_name: str,
    request: BulkInsertRequest,
    db: ExtensibleDB = Depends(get_db)
):
    """
    Insert multiple documents into a table
    
    Performs a bulk insert operation for high throughput. Limited to 10,000 documents per request.
    """
    if request.table_name != table_name:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Table name in URL must match table name in request body"
        )
    
    doc_ids = await db.insert_rows(request.table_name, request.data)
    return BulkInsertResponse(ids=doc_ids, count=len(doc_ids))


# Query endpoints
@app.get("/tables/{table_name}/documents", response_model=QueryResponse, tags=["Queries"])
async def get_all_documents(
    table_name: str,
    limit: int = None,
    offset: int = None,
    db: ExtensibleDB = Depends(get_db)
):
    """
    Get all documents from a table
    
    Returns all documents from the specified table. Use limit and offset for pagination.
    """
    data = await db.select_all(table_name)
    
    # Apply pagination if specified
    total = len(data)
    if offset:
        data = data[offset:]
    if limit:
        data = data[:limit]
    
    return QueryResponse(data=data, count=len(data), total=total)


@app.post("/tables/{table_name}/query", response_model=QueryResponse, tags=["Queries"])
async def query_by_index(
    table_name: str,
    request: QueryByIndexRequest,
    db: ExtensibleDB = Depends(get_db)
):
    """
    Query documents using a secondary index
    
    Performs a fast index-based query on the specified field and value.
    The field must have an index created for optimal performance.
    """
    if request.table_name != table_name:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Table name in URL must match table name in request body"
        )
    
    data = await db.select_by_index(
        request.table_name,
        request.field_name,
        request.value
    )
    return QueryResponse(data=data, count=len(data))


# Transaction endpoints
@app.post("/transactions/begin", response_model=TransactionResponse, tags=["Transactions"])
async def begin_transaction(db: ExtensibleDB = Depends(get_db)):
    """
    Begin a new transaction
    
    Starts a new database transaction. All subsequent operations will be part of this transaction
    until commit or rollback is called.
    """
    await db.begin_transaction()
    return TransactionResponse(
        operation="begin",
        message="Transaction started successfully"
    )


@app.post("/transactions/commit", response_model=TransactionResponse, tags=["Transactions"])
async def commit_transaction(db: ExtensibleDB = Depends(get_db)):
    """
    Commit the current transaction
    
    Commits all changes made in the current transaction to the database.
    """
    await db.commit_transaction()
    return TransactionResponse(
        operation="commit",
        message="Transaction committed successfully"
    )


@app.post("/transactions/rollback", response_model=TransactionResponse, tags=["Transactions"])
async def rollback_transaction(db: ExtensibleDB = Depends(get_db)):
    """
    Rollback the current transaction

    Discards all changes made in the current transaction.
    """
    await db.rollback_transaction()
    return TransactionResponse(
        operation="rollback",
        message="Transaction rolled back successfully"
    )


# Example domain-specific endpoints
@app.post("/users", response_model=InsertResponse, tags=["Examples - Users"])
async def create_user(user: User, db: ExtensibleDB = Depends(get_db)):
    """
    Create a new user

    Example endpoint demonstrating how to use domain-specific models.
    Creates the users table and email index if they don't exist.
    """
    try:
        await db.create_table("users")
        await db.create_index("users", "email")
        await db.create_index("users", "department")
    except ExtensibleDBError:
        pass  # Table/indexes might already exist

    user_data = model_to_dict(user)
    doc_id = await db.insert_row("users", user_data)
    return InsertResponse(id=doc_id, message="User created successfully")


@app.get("/users", response_model=QueryResponse, tags=["Examples - Users"])
async def get_users(
    limit: int = None,
    offset: int = None,
    db: ExtensibleDB = Depends(get_db)
):
    """Get all users with optional pagination"""
    data = await db.select_all("users")

    total = len(data)
    if offset:
        data = data[offset:]
    if limit:
        data = data[:limit]

    return QueryResponse(data=data, count=len(data), total=total)


@app.get("/users/by-department/{department}", response_model=QueryResponse, tags=["Examples - Users"])
async def get_users_by_department(department: str, db: ExtensibleDB = Depends(get_db)):
    """Get users by department using index query"""
    data = await db.select_by_index("users", "department", department)
    return QueryResponse(data=data, count=len(data))


@app.get("/users/by-email/{email}", response_model=QueryResponse, tags=["Examples - Users"])
async def get_user_by_email(email: str, db: ExtensibleDB = Depends(get_db)):
    """Get user by email using index query"""
    data = await db.select_by_index("users", "email", email)
    return QueryResponse(data=data, count=len(data))


@app.post("/products", response_model=InsertResponse, tags=["Examples - Products"])
async def create_product(product: Product, db: ExtensibleDB = Depends(get_db)):
    """
    Create a new product

    Example endpoint for product management.
    Creates the products table and category index if they don't exist.
    """
    try:
        await db.create_table("products")
        await db.create_index("products", "category")
        await db.create_index("products", "price")
    except ExtensibleDBError:
        pass  # Table/indexes might already exist

    product_data = model_to_dict(product)
    doc_id = await db.insert_row("products", product_data)
    return InsertResponse(id=doc_id, message="Product created successfully")


@app.get("/products", response_model=QueryResponse, tags=["Examples - Products"])
async def get_products(
    limit: int = None,
    offset: int = None,
    db: ExtensibleDB = Depends(get_db)
):
    """Get all products with optional pagination"""
    data = await db.select_all("products")

    total = len(data)
    if offset:
        data = data[offset:]
    if limit:
        data = data[:limit]

    return QueryResponse(data=data, count=len(data), total=total)


@app.get("/products/by-category/{category}", response_model=QueryResponse, tags=["Examples - Products"])
async def get_products_by_category(category: str, db: ExtensibleDB = Depends(get_db)):
    """Get products by category using index query"""
    data = await db.select_by_index("products", "category", category)
    return QueryResponse(data=data, count=len(data))


@app.post("/orders", response_model=InsertResponse, tags=["Examples - Orders"])
async def create_order(order: Order, db: ExtensibleDB = Depends(get_db)):
    """
    Create a new order

    Example endpoint for order management.
    Creates the orders table and indexes if they don't exist.
    """
    try:
        await db.create_table("orders")
        await db.create_index("orders", "user_id")
        await db.create_index("orders", "status")
    except ExtensibleDBError:
        pass  # Table/indexes might already exist

    order_data = model_to_dict(order)
    doc_id = await db.insert_row("orders", order_data)
    return InsertResponse(id=doc_id, message="Order created successfully")


@app.get("/orders", response_model=QueryResponse, tags=["Examples - Orders"])
async def get_orders(
    limit: int = None,
    offset: int = None,
    db: ExtensibleDB = Depends(get_db)
):
    """Get all orders with optional pagination"""
    data = await db.select_all("orders")

    total = len(data)
    if offset:
        data = data[offset:]
    if limit:
        data = data[:limit]

    return QueryResponse(data=data, count=len(data), total=total)


@app.get("/orders/by-user/{user_id}", response_model=QueryResponse, tags=["Examples - Orders"])
async def get_orders_by_user(user_id: int, db: ExtensibleDB = Depends(get_db)):
    """Get orders by user ID using index query"""
    data = await db.select_by_index("orders", "user_id", user_id)
    return QueryResponse(data=data, count=len(data))


@app.get("/orders/by-status/{status}", response_model=QueryResponse, tags=["Examples - Orders"])
async def get_orders_by_status(status: str, db: ExtensibleDB = Depends(get_db)):
    """Get orders by status using index query"""
    data = await db.select_by_index("orders", "status", status)
    return QueryResponse(data=data, count=len(data))


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
