"""
ExtensibleDB Python Wrapper

This module provides a Python interface to ExtensibleDB through subprocess calls.
It handles Lua script execution and JSON data exchange.
"""

import asyncio
import json
import subprocess
import tempfile
import os
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
import logging

logger = logging.getLogger(__name__)


class ExtensibleDBError(Exception):
    """Custom exception for ExtensibleDB operations"""
    pass


class ExtensibleDB:
    """
    Python wrapper for ExtensibleDB using subprocess communication.
    
    This class provides a Python interface to the Rust-based ExtensibleDB
    by executing Lua scripts and exchanging data via JSON.
    """
    
    def __init__(self, db_path: str = "./data.db", binary_path: str = None):
        """
        Initialize the ExtensibleDB wrapper.
        
        Args:
            db_path: Path to the database directory
            binary_path: Path to the ExtensibleDB binary (auto-detected if None)
        """
        self.db_path = Path(db_path)
        self.binary_path = self._find_binary(binary_path)
        self._validate_setup()
    
    def _find_binary(self, binary_path: Optional[str]) -> Path:
        """Find the ExtensibleDB binary"""
        if binary_path:
            return Path(binary_path)
        
        # Try common locations
        candidates = [
            Path("./target/release/extensibledb"),
            Path("./target/debug/extensibledb"),
            Path("../target/release/extensibledb"),
            Path("../target/debug/extensibledb"),
            Path("../../target/release/extensibledb"),
            Path("../../target/debug/extensibledb"),
        ]
        
        for candidate in candidates:
            if candidate.exists():
                return candidate
        
        raise ExtensibleDBError("ExtensibleDB binary not found. Please build it with 'cargo build --release'")
    
    def _validate_setup(self):
        """Validate that ExtensibleDB binary exists and is executable"""
        if not self.binary_path.exists():
            raise ExtensibleDBError(f"ExtensibleDB binary not found at {self.binary_path}")
        
        if not os.access(self.binary_path, os.X_OK):
            raise ExtensibleDBError(f"ExtensibleDB binary is not executable: {self.binary_path}")
    
    async def _execute_lua_script(self, script: str, timeout: float = 30.0) -> str:
        """
        Execute a Lua script using ExtensibleDB and return the output.
        
        Args:
            script: Lua script to execute
            timeout: Maximum execution time in seconds
            
        Returns:
            Script output as string
            
        Raises:
            ExtensibleDBError: If script execution fails
        """
        try:
            # Create temporary script file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.lua', delete=False) as f:
                f.write(script)
                script_path = f.name
            
            try:
                # Execute the script with timeout
                process = await asyncio.create_subprocess_exec(
                    str(self.binary_path),
                    script_path,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    cwd=self.db_path.parent
                )
                
                try:
                    stdout, stderr = await asyncio.wait_for(
                        process.communicate(), 
                        timeout=timeout
                    )
                except asyncio.TimeoutError:
                    process.kill()
                    await process.wait()
                    raise ExtensibleDBError(f"Script execution timed out after {timeout} seconds")
                
                if process.returncode != 0:
                    error_msg = stderr.decode('utf-8') if stderr else "Unknown error"
                    raise ExtensibleDBError(f"Script execution failed: {error_msg}")
                
                return stdout.decode('utf-8')
                
            finally:
                # Clean up temporary file
                try:
                    os.unlink(script_path)
                except OSError:
                    pass  # Ignore cleanup errors
                
        except Exception as e:
            if isinstance(e, ExtensibleDBError):
                raise
            raise ExtensibleDBError(f"Failed to execute Lua script: {str(e)}")
    
    async def create_table(self, table_name: str) -> bool:
        """
        Create a new table.
        
        Args:
            table_name: Name of the table to create
            
        Returns:
            True if successful
        """
        if not table_name or not table_name.replace('_', '').isalnum():
            raise ExtensibleDBError("Invalid table name")
            
        script = f"""
        db:create_table("{table_name}")
        print("Table '{table_name}' created successfully")
        """
        
        await self._execute_lua_script(script)
        return True
    
    async def create_index(self, table_name: str, field_name: str) -> bool:
        """
        Create a secondary index on a field.
        
        Args:
            table_name: Name of the table
            field_name: Name of the field to index
            
        Returns:
            True if successful
        """
        if not table_name or not field_name:
            raise ExtensibleDBError("Table name and field name are required")
            
        script = f"""
        db:create_index("{table_name}", "{field_name}")
        print("Index created on {table_name}.{field_name}")
        """
        
        await self._execute_lua_script(script)
        return True
    
    async def insert_row(self, table_name: str, data: Dict[str, Any]) -> int:
        """
        Insert a single row into a table.
        
        Args:
            table_name: Name of the table
            data: Dictionary containing the row data
            
        Returns:
            ID of the inserted row
        """
        if not table_name or not data:
            raise ExtensibleDBError("Table name and data are required")
            
        json_data = json.dumps(data)
        script = f"""
        local data = {json_data}
        local id = db:insert_row("{table_name}", data)
        print(id)
        """
        
        output = await self._execute_lua_script(script)
        try:
            return int(output.strip())
        except ValueError:
            raise ExtensibleDBError(f"Failed to parse inserted row ID: {output}")
    
    async def insert_rows(self, table_name: str, data_list: List[Dict[str, Any]]) -> List[int]:
        """
        Insert multiple rows into a table (bulk operation).
        
        Args:
            table_name: Name of the table
            data_list: List of dictionaries containing row data
            
        Returns:
            List of IDs of the inserted rows
        """
        if not table_name or not data_list:
            raise ExtensibleDBError("Table name and data list are required")
            
        if len(data_list) > 10000:  # Reasonable limit
            raise ExtensibleDBError("Bulk insert limited to 10,000 records")
            
        json_data = json.dumps(data_list)
        script = f"""
        local data = {json_data}
        local ids = db:insert_rows("{table_name}", data)
        for i, id in ipairs(ids) do
            print(id)
        end
        """
        
        output = await self._execute_lua_script(script)
        try:
            return [int(line.strip()) for line in output.strip().split('\n') if line.strip()]
        except ValueError:
            raise ExtensibleDBError(f"Failed to parse inserted row IDs: {output}")
    
    async def select_all(self, table_name: str) -> List[Dict[str, Any]]:
        """
        Select all rows from a table.
        
        Args:
            table_name: Name of the table
            
        Returns:
            List of dictionaries containing row data
        """
        if not table_name:
            raise ExtensibleDBError("Table name is required")
            
        script = f"""
        local rows = db:select_all("{table_name}")
        print(require('json').encode(rows))
        """
        
        output = await self._execute_lua_script(script)
        try:
            return json.loads(output.strip())
        except json.JSONDecodeError:
            raise ExtensibleDBError(f"Failed to parse JSON output: {output}")
    
    async def select_by_index(self, table_name: str, field_name: str, value: Any) -> List[Dict[str, Any]]:
        """
        Select rows using a secondary index.
        
        Args:
            table_name: Name of the table
            field_name: Name of the indexed field
            value: Value to search for
            
        Returns:
            List of dictionaries containing matching row data
        """
        if not table_name or not field_name:
            raise ExtensibleDBError("Table name and field name are required")
            
        json_value = json.dumps(value)
        script = f"""
        local value = {json_value}
        local rows = db:select_by_index("{table_name}", "{field_name}", value)
        print(require('json').encode(rows))
        """
        
        output = await self._execute_lua_script(script)
        try:
            return json.loads(output.strip())
        except json.JSONDecodeError:
            raise ExtensibleDBError(f"Failed to parse JSON output: {output}")
    
    async def begin_transaction(self) -> bool:
        """Begin a transaction"""
        script = "db:begin_transaction()"
        await self._execute_lua_script(script)
        return True
    
    async def commit_transaction(self) -> bool:
        """Commit the current transaction"""
        script = "db:commit_transaction()"
        await self._execute_lua_script(script)
        return True
    
    async def rollback_transaction(self) -> bool:
        """Rollback the current transaction"""
        script = "db:rollback_transaction()"
        await self._execute_lua_script(script)
        return True
    
    async def health_check(self) -> bool:
        """
        Perform a basic health check on the database.
        
        Returns:
            True if database is responsive
        """
        try:
            script = 'print("health_ok")'
            output = await self._execute_lua_script(script, timeout=5.0)
            return "health_ok" in output
        except Exception:
            return False
