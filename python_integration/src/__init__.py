"""
ExtensibleDB Python Integration Package

This package provides Python bindings and FastAPI integration for ExtensibleDB,
a high-performance embedded database engine written in Rust.
"""

__version__ = "1.0.0"
__author__ = "ExtensibleDB Team"
__email__ = "<EMAIL>"

from .db_wrapper import ExtensibleDB, ExtensibleDBError
from .models import (
    User, Product, Order,
    TableCreateRequest, IndexCreateRequest,
    DocumentInsertRequest, BulkInsertRequest,
    QueryByIndexRequest, TableQueryRequest,
    SuccessResponse, ErrorResponse,
    InsertResponse, BulkInsertResponse,
    QueryResponse, TransactionResponse
)

__all__ = [
    "ExtensibleDB",
    "ExtensibleDBError",
    "User",
    "Product", 
    "Order",
    "TableCreateRequest",
    "IndexCreateRequest",
    "DocumentInsertRequest",
    "BulkInsertRequest",
    "QueryByIndexRequest",
    "TableQueryRequest",
    "SuccessResponse",
    "ErrorResponse",
    "InsertResponse",
    "BulkInsertResponse",
    "QueryResponse",
    "TransactionResponse",
]
