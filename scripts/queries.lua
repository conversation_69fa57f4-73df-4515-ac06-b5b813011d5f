-- scripts/queries.lua - Example queries and data analysis
-- Run with: cargo run scripts/queries.lua

print("📊 ExtensibleDB Query Examples")
print("=" .. string.rep("=", 40))

-- Query 1: List all customers
print("\n1️⃣  All Customers:")
local customers = db:select_all("customers")
for _, customer in ipairs(customers) do
    print("  • " .. customer.name .. " (" .. customer.city .. ") - " .. customer.email)
end

-- Query 2: Product inventory
print("\n2️⃣  Product Inventory:")
local products = db:select_all("products")
local total_value = 0

print("Product                | Price    | Stock | Value")
print("-----------------------|----------|-------|----------")

for _, product in ipairs(products) do
    local value = product.price * product.stock
    total_value = total_value + value
    
    print(string.format("%-22s | $%-7.2f | %-5d | $%-7.2f", 
          product.name, product.price, product.stock, value))
end

print("-----------------------|----------|-------|----------")
print(string.format("%-22s | %-8s | %-5s | $%-7.2f", 
      "TOTAL INVENTORY VALUE", "", "", total_value))

-- Query 3: Order analysis
print("\n3️⃣  Order Analysis:")
local orders = db:select_all("orders")

if #orders > 0 then
    local total_revenue = 0
    local order_count = #orders
    
    print("Recent Orders:")
    for _, order in ipairs(orders) do
        total_revenue = total_revenue + order.total
        print("  • Order #" .. order.id .. ": " .. order.customer_name .. 
              " - " .. order.quantity .. "x " .. order.product_name .. 
              " = $" .. string.format("%.2f", order.total))
    end
    
    local avg_order = total_revenue / order_count
    print("\n📈 Order Statistics:")
    print("  Total Orders: " .. order_count)
    print("  Total Revenue: $" .. string.format("%.2f", total_revenue))
    print("  Average Order Value: $" .. string.format("%.2f", avg_order))
else
    print("  No orders found. Run setup.lua first!")
end

-- Query 4: Customer analysis
print("\n4️⃣  Customer Analysis:")
local customer_orders = {}

-- Group orders by customer
for _, order in ipairs(orders) do
    local customer_id = order.customer_id
    if not customer_orders[customer_id] then
        customer_orders[customer_id] = {
            name = order.customer_name,
            orders = 0,
            total_spent = 0
        }
    end
    customer_orders[customer_id].orders = customer_orders[customer_id].orders + 1
    customer_orders[customer_id].total_spent = customer_orders[customer_id].total_spent + order.total
end

print("Customer               | Orders | Total Spent")
print("-----------------------|--------|------------")

for customer_id, data in pairs(customer_orders) do
    print(string.format("%-22s | %-6d | $%-9.2f", 
          data.name, data.orders, data.total_spent))
end

-- Query 5: Low stock alert
print("\n5️⃣  Low Stock Alert (< 20 items):")
local low_stock = {}

for _, product in ipairs(products) do
    if product.stock < 20 then
        table.insert(low_stock, product)
    end
end

if #low_stock > 0 then
    for _, product in ipairs(low_stock) do
        print("  ⚠️  " .. product.name .. ": " .. product.stock .. " remaining")
    end
else
    print("  ✅ All products have sufficient stock")
end

print("\n" .. string.rep("=", 50))
print("🎯 Query examples completed!")
print("💡 Try modifying these queries or create your own!")
