-- scripts/setup.lua - Database setup and sample data
-- Run with: cargo run scripts/setup.lua

print("🔧 Setting up ExtensibleDB with sample data...")

-- Create tables
db:create_table("products")
db:create_table("orders")
db:create_table("customers")

-- Add sample customers
local customers = {
    {name = "<PERSON> Johnson", email = "<EMAIL>", city = "New York"},
    {name = "<PERSON>", email = "<EMAIL>", city = "Los Angeles"},
    {name = "<PERSON>", email = "<EMAIL>", city = "Chicago"},
    {name = "<PERSON>", email = "<EMAIL>", city = "Houston"}
}

print("👥 Adding customers...")
for _, customer in ipairs(customers) do
    customer.id = math.random(1000, 9999)
    customer.created_at = os.time()
    db:insert_row("customers", customer)
    print("  ✅ " .. customer.name)
end

-- Add sample products
local products = {
    {name = "Laptop", price = 999.99, category = "Electronics", stock = 10},
    {name = "Mouse", price = 29.99, category = "Electronics", stock = 50},
    {name = "Keyboard", price = 79.99, category = "Electronics", stock = 25},
    {name = "Monitor", price = 299.99, category = "Electronics", stock = 15},
    {name = "Desk Chair", price = 199.99, category = "Furniture", stock = 8}
}

print("📦 Adding products...")
for _, product in ipairs(products) do
    product.id = math.random(10000, 99999)
    product.created_at = os.time()
    db:insert_row("products", product)
    print("  ✅ " .. product.name .. " ($" .. product.price .. ")")
end

-- Add sample orders
print("🛒 Adding orders...")
local customer_list = db:select_all("customers")
local product_list = db:select_all("products")

for i = 1, 5 do
    local customer = customer_list[math.random(#customer_list)]
    local product = product_list[math.random(#product_list)]
    local quantity = math.random(1, 3)
    
    local order = {
        id = math.random(100000, 999999),
        customer_id = customer.id,
        customer_name = customer.name,
        product_id = product.id,
        product_name = product.name,
        quantity = quantity,
        unit_price = product.price,
        total = product.price * quantity,
        order_date = os.date("%Y-%m-%d"),
        created_at = os.time()
    }
    
    db:insert_row("orders", order)
    print("  ✅ Order #" .. order.id .. ": " .. customer.name .. " bought " .. quantity .. "x " .. product.name)
end

print("\n🎉 Setup complete! Database populated with sample data.")
print("💡 Try running: cargo run scripts/queries.lua")
