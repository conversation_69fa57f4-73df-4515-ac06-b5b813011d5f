# Rust
/target/
**/*.rs.bk
*.pdb
Cargo.lock

# Database files
data.db/
*.db
*.sqlite
*.sqlite3

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store

# OS generated files
Thumbs.db
ehthumbs.db
Desktop.ini

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp
.cache/

# Backup files
*.bak
*.backup

# Test artifacts
test_output/
bench_results/

# Local configuration
.env
.env.local
config.local.*

# Documentation build artifacts
book/
docs/_build/

# Profiling data
*.prof
perf.data*
flamegraph.svg
