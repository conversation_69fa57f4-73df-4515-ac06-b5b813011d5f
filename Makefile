# ExtensibleDB Makefile

.PHONY: build test clean run demo benchmark help

# Default target
help:
	@echo "ExtensibleDB - High-Performance Embedded Database"
	@echo "================================================="
	@echo ""
	@echo "Available targets:"
	@echo "  build      - Build the project (release mode)"
	@echo "  test       - Run all tests (unit + integration)"
	@echo "  demo       - Run the feature demonstration"
	@echo "  benchmark  - Run performance benchmarks"
	@echo "  run        - Start interactive console"
	@echo "  clean      - Clean build artifacts and database"
	@echo "  help       - Show this help message"

# Build the project
build:
	@echo "🔨 Building ExtensibleDB..."
	cargo build --release

# Run all tests
test:
	@echo "🧪 Running all tests..."
	./run_tests.sh

# Run the demo
demo:
	@echo "🎪 Running feature demonstration..."
	cargo run --release examples/demo.lua

# Run benchmarks
benchmark:
	@echo "📊 Running performance benchmarks..."
	cargo run --release examples/benchmark.lua

# Start interactive console
run:
	@echo "🚀 Starting ExtensibleDB console..."
	cargo run --release

# Clean everything
clean:
	@echo "🧹 Cleaning up..."
	cargo clean
	rm -rf data.db/
	@echo "✅ Clean complete"

# Quick development cycle
dev: clean build test
	@echo "🎉 Development cycle complete!"
