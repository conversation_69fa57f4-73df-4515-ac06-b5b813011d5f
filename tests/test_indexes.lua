-- test_indexes.lua - Test secondary index functionality
print("🧪 Testing Secondary Index Functionality")
print("=" .. string.rep("=", 50))

-- Test 1: Create table and indexes
print("\n1️⃣  Testing index creation...")
db:create_table("test_indexes")
db:create_index("test_indexes", "email")
db:create_index("test_indexes", "age")
print("✅ Created table and indexes for 'email' and 'age'")

-- Test 2: Insert data with indexed fields
print("\n2️⃣  Testing data insertion with indexes...")
local user_ids = {}

user_ids[1] = db:insert_row("test_indexes", {
    name = "<PERSON>",
    email = "<EMAIL>",
    age = 30
})

user_ids[2] = db:insert_row("test_indexes", {
    name = "<PERSON>", 
    email = "<EMAIL>",
    age = 25
})

user_ids[3] = db:insert_row("test_indexes", {
    name = "<PERSON>",
    email = "<EMAIL>", 
    age = 30
})

print("✅ Inserted 3 users with IDs:", table.concat(user_ids, ", "))

-- Test 3: Query by email index
print("\n3️⃣  Testing email index queries...")
local alice_results = db:select_by_index("test_indexes", "email", "<EMAIL>")
print("✅ Found", #alice_results, "user(s) with email '<EMAIL>'")
if #alice_results > 0 then
    print("   Name:", alice_results[1].name)
end

local bob_results = db:select_by_index("test_indexes", "email", "<EMAIL>")
print("✅ Found", #bob_results, "user(s) with email '<EMAIL>'")
if #bob_results > 0 then
    print("   Name:", bob_results[1].name)
end

-- Test 4: Query by age index
print("\n4️⃣  Testing age index queries...")
local age_30_results = db:select_by_index("test_indexes", "age", 30)
print("✅ Found", #age_30_results, "user(s) with age 30")
for i, user in ipairs(age_30_results) do
    print("   " .. i .. ". " .. user.name)
end

local age_25_results = db:select_by_index("test_indexes", "age", 25)
print("✅ Found", #age_25_results, "user(s) with age 25")
for i, user in ipairs(age_25_results) do
    print("   " .. i .. ". " .. user.name)
end

-- Test 5: Bulk insert with indexes
print("\n5️⃣  Testing bulk insert with indexes...")
local bulk_users = {
    {name = "David", email = "<EMAIL>", age = 28},
    {name = "Eve", email = "<EMAIL>", age = 32},
    {name = "Frank", email = "<EMAIL>", age = 25}
}

local bulk_ids = db:insert_rows("test_indexes", bulk_users)
print("✅ Bulk inserted", #bulk_ids, "users with IDs:", table.concat(bulk_ids, ", "))

-- Test 6: Verify indexes work after bulk insert
print("\n6️⃣  Testing indexes after bulk insert...")
local all_age_25 = db:select_by_index("test_indexes", "age", 25)
print("✅ Found", #all_age_25, "user(s) with age 25 (should be 2)")
for i, user in ipairs(all_age_25) do
    print("   " .. i .. ". " .. user.name)
end

-- Test 7: Verify all data
print("\n7️⃣  Verifying all data...")
local all_users = db:select_all("test_indexes")
print("✅ Total users in database:", #all_users)

print("\nAll Users:")
for i, user in ipairs(all_users) do
    print(string.format("  %d. %s (%s) - Age: %s", 
          i, user.name or "Unknown", user.email or "No email", user.age or "N/A"))
end

print("\n" .. string.rep("=", 50))
print("🎉 Secondary index functionality test completed successfully!")
print("💡 Indexes are working correctly for both single and bulk inserts")
