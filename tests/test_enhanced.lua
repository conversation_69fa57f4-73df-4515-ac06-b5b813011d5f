-- test_enhanced.lua - Test enhanced functionality
print("🧪 Testing Enhanced ExtensibleDB Functionality")
print("=" .. string.rep("=", 50))

-- Test 1: Single insert with ID return
print("\n1️⃣  Testing single insert with ID return...")
db:create_table("test_enhanced")

local user_id = db:insert_row("test_enhanced", {
    name = "<PERSON>",
    email = "<EMAIL>",
    age = 30
})
print("✅ Inserted user with ID:", user_id)

-- Test 2: Bulk insert
print("\n2️⃣  Testing bulk insert...")
local bulk_users = {
    {name = "<PERSON>", email = "<EMAIL>", age = 25},
    {name = "<PERSON>", email = "<EMAIL>", age = 35},
    {name = "<PERSON>", email = "<EMAIL>", age = 28}
}

local bulk_ids = db:insert_rows("test_enhanced", bulk_users)
print("✅ Bulk inserted", #bulk_ids, "users with IDs:", table.concat(bulk_ids, ", "))

-- Test 3: Verify all data
print("\n3️⃣  Verifying all data...")
local all_users = db:select_all("test_enhanced")
print("✅ Total users in database:", #all_users)

print("\nUser List:")
for i, user in ipairs(all_users) do
    print(string.format("  %d. %s (%s) - Age: %s", 
          i, user.name or "Unknown", user.email or "No email", user.age or "N/A"))
end

-- Test 4: Performance test (small scale)
print("\n4️⃣  Performance test...")
local start_time = os.clock()

-- Insert 100 records in bulk
local perf_users = {}
for i = 1, 100 do
    table.insert(perf_users, {
        name = "User" .. i,
        email = "user" .. i .. "@perf.com",
        age = 20 + (i % 40)
    })
end

db:create_table("perf_test")
local perf_ids = db:insert_rows("perf_test", perf_users)

local end_time = os.clock()
local duration = end_time - start_time

print("✅ Inserted 100 users in", string.format("%.3f", duration), "seconds")
print("   Rate:", string.format("%.0f", 100 / duration), "inserts/second")

print("\n" .. string.rep("=", 50))
print("🎉 Enhanced functionality test completed successfully!")
print("💡 Bulk operations and ID returns are working correctly")
