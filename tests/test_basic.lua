-- test_basic.lua - Test basic functionality
print("🧪 Testing Basic ExtensibleDB Functionality")
print("=" .. string.rep("=", 40))

-- Test 1: Create table and insert data
print("\n1️⃣  Testing basic operations...")
db:create_table("test_basic")

-- Insert some test data
db:insert_row("test_basic", {name = "<PERSON>", age = 30})
db:insert_row("test_basic", {name = "<PERSON>", age = 25})
db:insert_row("test_basic", {name = "<PERSON>", age = 35})

print("✅ Inserted 3 test records")

-- Test 2: Select all data
print("\n2️⃣  Testing select all...")
local all_records = db:select_all("test_basic")
print("✅ Retrieved", #all_records, "records")

for i, record in ipairs(all_records) do
    print(string.format("  %d. %s (age %s)", i, record.name, record.age))
end

print("\n🎉 Basic functionality test completed successfully!")
print("💡 The database is working correctly")
