-- benchmark.lua - Performance benchmarks for ExtensibleDB
print("🏁 ExtensibleDB Performance Benchmarks")
print("=" .. string.rep("=", 50))

-- Helper function to format numbers
function format_number(num)
    local formatted = tostring(num)
    local k = 3
    while k < #formatted do
        formatted = formatted:sub(1, #formatted - k) .. "," .. formatted:sub(#formatted - k + 1)
        k = k + 4
    end
    return formatted
end

-- Benchmark 1: Single inserts
print("\n🔥 Benchmark 1: Single Insert Performance")
db:create_table("bench_single")

local start_time = os.clock()
local single_count = 1000

for i = 1, single_count do
    db:insert_row("bench_single", {
        id = i,
        name = "User" .. i,
        email = "user" .. i .. "@bench.com",
        score = math.random(1, 100)
    })
end

local single_time = os.clock() - start_time
local single_rate = single_count / single_time

print("✅ Inserted", format_number(single_count), "records in", string.format("%.3f", single_time), "seconds")
print("   Rate:", format_number(math.floor(single_rate)), "inserts/second")

-- Benchmark 2: Bulk inserts
print("\n🔥 Benchmark 2: Bulk Insert Performance")
db:create_table("bench_bulk")

local bulk_data = {}
local bulk_count = 5000

for i = 1, bulk_count do
    table.insert(bulk_data, {
        id = i,
        name = "BulkUser" .. i,
        email = "bulk" .. i .. "@bench.com",
        score = math.random(1, 100),
        category = "cat" .. (i % 10)
    })
end

start_time = os.clock()
local bulk_ids = db:insert_rows("bench_bulk", bulk_data)
local bulk_time = os.clock() - start_time
local bulk_rate = bulk_count / bulk_time

print("✅ Bulk inserted", format_number(bulk_count), "records in", string.format("%.3f", bulk_time), "seconds")
print("   Rate:", format_number(math.floor(bulk_rate)), "inserts/second")
print("   Speedup:", string.format("%.1fx", bulk_rate / single_rate), "faster than single inserts")

-- Benchmark 3: Index creation and queries
print("\n🔥 Benchmark 3: Index Performance")
db:create_index("bench_bulk", "category")
db:create_index("bench_bulk", "score")

-- Benchmark index queries
start_time = os.clock()
local query_count = 100

for i = 1, query_count do
    local category = "cat" .. (i % 10)
    local results = db:select_by_index("bench_bulk", "category", category)
end

local index_query_time = os.clock() - start_time
local index_query_rate = query_count / index_query_time

print("✅ Performed", format_number(query_count), "index queries in", string.format("%.3f", index_query_time), "seconds")
print("   Rate:", format_number(math.floor(index_query_rate)), "queries/second")

-- Benchmark 4: Full table scans
print("\n🔥 Benchmark 4: Full Table Scan Performance")
start_time = os.clock()
local scan_count = 50

for i = 1, scan_count do
    local all_records = db:select_all("bench_bulk")
end

local scan_time = os.clock() - start_time
local scan_rate = scan_count / scan_time

print("✅ Performed", format_number(scan_count), "full table scans in", string.format("%.3f", scan_time), "seconds")
print("   Rate:", string.format("%.1f", scan_rate), "scans/second")
print("   Records per scan:", format_number(bulk_count))

-- Benchmark 5: Mixed workload
print("\n🔥 Benchmark 5: Mixed Workload Performance")
db:create_table("bench_mixed")
db:create_index("bench_mixed", "status")

start_time = os.clock()
local mixed_ops = 1000

for i = 1, mixed_ops do
    if i % 10 == 0 then
        -- 10% reads
        local results = db:select_by_index("bench_mixed", "status", "active")
    else
        -- 90% writes
        db:insert_row("bench_mixed", {
            id = i,
            name = "MixedUser" .. i,
            status = (i % 2 == 0) and "active" or "inactive",
            timestamp = os.time()
        })
    end
end

local mixed_time = os.clock() - start_time
local mixed_rate = mixed_ops / mixed_time

print("✅ Performed", format_number(mixed_ops), "mixed operations in", string.format("%.3f", mixed_time), "seconds")
print("   Rate:", format_number(math.floor(mixed_rate)), "operations/second")
print("   Mix: 90% writes, 10% index reads")

-- Summary
print("\n" .. string.rep("=", 50))
print("📊 Performance Summary:")
print("   Single Inserts:  ", format_number(math.floor(single_rate)), "ops/sec")
print("   Bulk Inserts:    ", format_number(math.floor(bulk_rate)), "ops/sec")
print("   Index Queries:   ", format_number(math.floor(index_query_rate)), "ops/sec")
print("   Table Scans:     ", string.format("%.1f", scan_rate), "ops/sec")
print("   Mixed Workload:  ", format_number(math.floor(mixed_rate)), "ops/sec")

print("\n🎯 ExtensibleDB Performance Characteristics:")
print("   ✓ Excellent bulk insert performance")
print("   ✓ Fast secondary index queries")
print("   ✓ Efficient mixed read/write workloads")
print("   ✓ Maintained indexes during high-throughput inserts")

print("\n🚀 Ready for high-performance applications!")
