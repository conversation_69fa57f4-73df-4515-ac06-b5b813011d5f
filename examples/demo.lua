-- demo.lua - Demonstration of Enhanced ExtensibleDB Features
print("🚀 ExtensibleDB Enhanced Features Demo")
print("=" .. string.rep("=", 50))

print("\n🎯 Welcome to ExtensibleDB - A High-Performance Embedded Database!")
print("   Combining SQLite's portability with PostgreSQL-like extensibility")
print("   Enhanced with maintained secondary indexes and bulk operations")

-- Demo 1: Basic Operations with ID Returns
print("\n📝 Demo 1: Enhanced Basic Operations")
db:create_table("demo_users")

local alice_id = db:insert_row("demo_users", {
    name = "<PERSON>",
    email = "<EMAIL>",
    department = "Engineering",
    salary = 95000,
    hire_date = "2023-01-15"
})

print("✅ Inserted Alice with ID:", alice_id)

-- Demo 2: Bulk Operations
print("\n📦 Demo 2: High-Performance Bulk Operations")
local employees = {
    {name = "<PERSON>", email = "<EMAIL>", department = "Engineering", salary = 87000, hire_date = "2023-02-01"},
    {name = "<PERSON>", email = "<EMAIL>", department = "Marketing", salary = 72000, hire_date = "2023-02-15"},
    {name = "<PERSON>", email = "<EMAIL>", department = "Sales", salary = 68000, hire_date = "2023-03-01"},
    {name = "Eve <PERSON>", email = "<EMAIL>", department = "Engineering", salary = 92000, hire_date = "2023-03-15"},
    {name = "Frank Miller", email = "<EMAIL>", department = "HR", salary = 65000, hire_date = "2023-04-01"}
}

local bulk_ids = db:insert_rows("demo_users", employees)
print("✅ Bulk inserted", #bulk_ids, "employees in one operation")
print("   IDs:", table.concat(bulk_ids, ", "))

-- Demo 3: Secondary Indexes
print("\n🔍 Demo 3: Lightning-Fast Secondary Indexes")
db:create_index("demo_users", "department")
db:create_index("demo_users", "salary")
db:create_index("demo_users", "email")

print("✅ Created indexes on department, salary, and email")

-- Query by department
local engineers = db:select_by_index("demo_users", "department", "Engineering")
print("✅ Found", #engineers, "engineers using department index")

-- Query by salary
local high_earners = db:select_by_index("demo_users", "salary", 95000)
print("✅ Found", #high_earners, "employee(s) earning $95,000")

-- Demo 4: Complex Queries
print("\n🧮 Demo 4: Complex Query Scenarios")

-- Find all employees by department
local departments = {}
local all_employees = db:select_all("demo_users")

for _, emp in ipairs(all_employees) do
    local dept = emp.department
    if not departments[dept] then
        departments[dept] = {}
    end
    table.insert(departments[dept], emp)
end

print("✅ Employee distribution by department:")
for dept, emps in pairs(departments) do
    local total_salary = 0
    for _, emp in ipairs(emps) do
        total_salary = total_salary + emp.salary
    end
    local avg_salary = total_salary / #emps
    print(string.format("   %s: %d employees, avg salary: $%.0f", dept, #emps, avg_salary))
end

-- Demo 5: Transaction Support
print("\n💾 Demo 5: Transaction Support")
db:begin_transaction()

db:insert_row("demo_users", {
    name = "Grace Lee",
    email = "<EMAIL>", 
    department = "Finance",
    salary = 78000,
    hire_date = "2023-05-01"
})

db:commit_transaction()
print("✅ Transaction completed - Grace added to Finance department")

-- Demo 6: Performance Showcase
print("\n⚡ Demo 6: Performance Showcase")
db:create_table("performance_demo")
db:create_index("performance_demo", "category")

local start_time = os.clock()

-- Generate and insert test data
local test_data = {}
for i = 1, 1000 do
    table.insert(test_data, {
        id = i,
        name = "Record" .. i,
        category = "Cat" .. (i % 20),
        value = math.random(1, 1000),
        timestamp = os.time()
    })
end

local perf_ids = db:insert_rows("performance_demo", test_data)
local insert_time = os.clock() - start_time

print("✅ Inserted 1,000 records with maintained indexes in", string.format("%.3f", insert_time), "seconds")
print("   Rate:", string.format("%.0f", 1000 / insert_time), "inserts/second")

-- Test index query performance
start_time = os.clock()
local category_results = db:select_by_index("performance_demo", "category", "Cat5")
local query_time = os.clock() - start_time

print("✅ Index query found", #category_results, "results in", string.format("%.3f", query_time), "seconds")

-- Final Summary
print("\n" .. string.rep("=", 50))
print("🎉 ExtensibleDB Enhanced Features Summary:")
print("   ✓ High-performance bulk operations")
print("   ✓ Maintained secondary indexes")
print("   ✓ Fast index-based queries")
print("   ✓ Transaction support")
print("   ✓ ID returns for tracking")
print("   ✓ Lua programmability")
print("   ✓ SQLite-like portability")
print("   ✓ PostgreSQL-like extensibility")

print("\n🚀 ExtensibleDB is now ready to compete with PostgreSQL")
print("   in performance while maintaining SQLite's simplicity!")

print("\n💡 Try these commands in the REPL:")
print("   show_stats()           - View database statistics")
print("   list_users()           - List all users")
print("   add_user(name, email, age) - Add a new user")

print("\n🔧 Available test scripts:")
print("   cargo run test_basic.lua        - Basic functionality")
print("   cargo run test_enhanced.lua     - Enhanced features")
print("   cargo run test_indexes.lua      - Secondary indexes")
print("   cargo run test_comprehensive.lua - Full feature test")
print("   cargo run benchmark.lua         - Performance benchmarks")
