-- Debug composite indexes
print("🔍 Debugging Composite Indexes")

-- Create a simple test
db:create_table("debug_test")

-- Insert a single row first
local id = db:insert_row("debug_test", {
    field1 = "A",
    field2 = "B",
    name = "Test Item"
})
print("✅ Inserted row with ID:", id)

-- Now create the composite index
db:create_composite_index("debug_test", "test_index", {"field1", "field2"})
print("✅ Created composite index")

-- Try to query
local results = db:select_by_composite_index("debug_test", "test_index", {
    field1 = "A",
    field2 = "B"
})
print("📊 Query results:", #results)

-- Let's also check if we can find the row with regular queries
local all_rows = db:select_all("debug_test")
print("📊 Total rows in table:", #all_rows)
for _, row in ipairs(all_rows) do
    print("   Row:", row.name, "field1=" .. tostring(row.field1), "field2=" .. tostring(row.field2))
end
